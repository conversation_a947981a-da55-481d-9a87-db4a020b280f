import { populateTemplate } from "../templating.server";
import type { EmailTemplate } from "../templating.server";

const subjectTemplate = "Welcome to Our Platform, {{name}}!";
const htmlTemplate = `
  <h1>Welcome, {{name}}!</h1>
  <p>Thanks for signing up to our platform. We're excited to have you.</p>
  <p>If you have any questions, feel free to reply to this email.</p>
  <br>
  <p>Best Regards,</p>
  <p>The Platform Team</p>
`;
const textTemplate = `
  Welcome, {{name}}!

  Thanks for signing up to our platform. We're excited to have you.
  If you have any questions, feel free to reply to this email.

  Best Regards,
  The Platform Team
`;

export const welcomeTemplate: EmailTemplate = {
  subject: (vars) => populateTemplate(subjectTemplate, vars || {}),
  html: (vars) => populateTemplate(htmlTemplate, vars || {}),
  text: (vars) => populateTemplate(textTemplate, vars || {}),
};
