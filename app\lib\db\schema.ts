import { relations } from "drizzle-orm";
import {
  boolean,
  decimal,
  index,
  integer,
  jsonb,
  pgEnum,
  pgTable,
  uuid as pgUuid,
  primaryKey,
  serial,
  text,
  timestamp,
  unique,
  varchar,
} from "drizzle-orm/pg-core";

// Enums
export const billingProviderEnum = pgEnum("billing_provider", [
  "stripe",
  "lemon-squeezy",
  "paddle",
]);
export const subscriptionStatusEnum = pgEnum("subscription_status", [
  "active",
  "trialing",
  "past_due",
  "canceled",
  "unpaid",
  "incomplete",
  "incomplete_expired",
  "paused",
]);
export const paymentStatusEnum = pgEnum("payment_status", ["pending", "succeeded", "failed"]);
export const notificationChannelEnum = pgEnum("notification_channel", ["in_app", "email"]);
export const notificationTypeEnum = pgEnum("notification_type", [
  "info",
  "warning",
  "error",
  "success",
]);
export const appPermissionsEnum = pgEnum("app_permissions", [
  "roles.manage",
  "billing.manage",
  "settings.manage",
  "members.manage",
  "invites.manage",
  "api.manage",
]);

// Accounts table - Multi-tenant support
export const accounts = pgTable(
  "accounts",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    name: varchar("name", { length: 255 }).notNull(),
    slug: varchar("slug", { length: 100 }).unique(),
    email: varchar("email", { length: 255 }),
    pictureUrl: text("picture_url"),
    isPersonalAccount: boolean("is_personal_account").default(true).notNull(),
    primaryOwnerUserId: pgUuid("primary_owner_user_id").notNull(),
    publicData: jsonb("public_data"), // JSON object for flexible data
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow().notNull(),
    createdBy: pgUuid("created_by"),
    updatedBy: pgUuid("updated_by"),
  },
  (table) => ({
    nameIdx: index("accounts_name_idx").on(table.name),
    slugIdx: index("accounts_slug_idx").on(table.slug),
    primaryOwnerIdx: index("accounts_primary_owner_idx").on(table.primaryOwnerUserId),
  })
);

// Roles table - RBAC support
export const roles = pgTable("roles", {
  name: text("name").primaryKey(),
  hierarchyLevel: integer("hierarchy_level").notNull(),
});

// Role permissions table
export const rolePermissions = pgTable(
  "role_permissions",
  {
    id: serial("id").primaryKey(),
    role: text("role")
      .notNull()
      .references(() => roles.name, { onDelete: "cascade" }),
    permission: appPermissionsEnum("permission").notNull(),
  },
  (table) => ({
    rolePermissionUnique: unique("role_permission_unique").on(table.role, table.permission),
    roleIdx: index("role_permissions_role_idx").on(table.role),
  })
);

// Account memberships table - Multi-tenant user relationships
export const accountsMemberships = pgTable(
  "accounts_memberships",
  {
    accountId: text("account_id")
      .notNull()
      .references(() => accounts.id, { onDelete: "cascade" }),
    userId: text("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    accountRole: text("account_role")
      .notNull()
      .references(() => roles.name),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
    createdBy: text("created_by").references(() => users.id),
    updatedBy: text("updated_by").references(() => users.id),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.accountId, table.userId] }),
    accountIdIdx: index("accounts_memberships_account_id_idx").on(table.accountId),
    userIdIdx: index("accounts_memberships_user_id_idx").on(table.userId),
  })
);

// Users table - Enhanced with all required fields
export const users = pgTable(
  "users",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    uuid: pgUuid("uuid").notNull().unique().defaultRandom(), // User UUID for external references
    name: varchar("name", { length: 255 }).notNull(),
    email: varchar("email", { length: 255 }).notNull().unique(),
    avatar: text("avatar"), // Avatar URL
    credits: integer("credits").default(0).notNull(), // User credits balance
    inviteCode: varchar("invite_code", { length: 50 }).unique(), // User's invite code
    invitedBy: pgUuid("invited_by"), // Who invited this user (user UUID)

    signinType: varchar("signin_type", { length: 50 }), // oauth, email, etc.
    signinProvider: varchar("signin_provider", { length: 50 }), // google, github, etc.
    signinOpenid: text("signin_openid"), // OpenID from provider
    signinIp: varchar("signin_ip", { length: 45 }), // Last signin IP (IPv6 support)
    isAffiliate: boolean("is_affiliate").default(false).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => ({
    emailIdx: index("users_email_idx").on(table.email),
    uuidIdx: index("users_uuid_idx").on(table.uuid),
    inviteCodeIdx: index("users_invite_code_idx").on(table.inviteCode),
    creditsIdx: index("users_credits_idx").on(table.credits),
    affiliateIdx: index("users_affiliate_idx").on(table.isAffiliate),
  })
);

// Sessions table - For JWT session management
export const sessions = pgTable(
  "sessions",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    userId: pgUuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    sessionToken: varchar("session_token", { length: 255 }).notNull().unique(),
    refreshToken: varchar("refresh_token", { length: 255 }).unique(),
    expiresAt: timestamp("expires_at", { withTimezone: true }).notNull(),
    refreshExpiresAt: timestamp("refresh_expires_at", { withTimezone: true }),
    userAgent: text("user_agent"),
    ipAddress: varchar("ip_address", { length: 45 }),
    isActive: boolean("is_active").default(true).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => ({
    sessionTokenIdx: index("sessions_session_token_idx").on(table.sessionToken),
    userIdIdx: index("sessions_user_id_idx").on(table.userId),
    expiresAtIdx: index("sessions_expires_at_idx").on(table.expiresAt),
    isActiveIdx: index("sessions_is_active_idx").on(table.isActive),
  })
);

// Affiliates table - Enhanced affiliate marketing system
export const affiliates = pgTable(
  "affiliates",
  {
    id: serial("id").primaryKey(),
    userUuid: pgUuid("user_uuid").notNull(), // The affiliate user (references users.uuid)
    invitedBy: pgUuid("invited_by").notNull(), // Who invited this affiliate (references users.uuid)
    paidOrderNo: varchar("paid_order_no", { length: 100 }).default(""),
    paidAmount: integer("paid_amount").default(0).notNull(),
    rewardPercent: integer("reward_percent").default(0).notNull(),
    rewardAmount: integer("reward_amount").default(0).notNull(),
    status: varchar("status", { length: 20 }).notNull().default("active"), // active, inactive, suspended
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => ({
    userUuidIdx: index("affiliates_user_uuid_idx").on(table.userUuid),
    invitedByIdx: index("affiliates_invited_by_idx").on(table.invitedBy),
    statusIdx: index("affiliates_status_idx").on(table.status),
  })
);

// API Keys table - API access management
export const apiKeys = pgTable(
  "api_keys",
  {
    id: serial("id").primaryKey(),
    apiKey: varchar("api_key", { length: 255 }).notNull().unique(),
    title: varchar("title", { length: 255 }).notNull(),
    userUuid: pgUuid("user_uuid").notNull(), // References users.uuid
    accountId: pgUuid("account_id").references(() => accounts.id), // Optional: for multi-tenant API keys
    status: varchar("status", { length: 20 }).notNull().default("active"), // active, inactive, revoked
    lastUsedAt: timestamp("last_used_at", { withTimezone: true }),
    expiresAt: timestamp("expires_at", { withTimezone: true }),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => ({
    apiKeyIdx: index("api_keys_api_key_idx").on(table.apiKey),
    userUuidIdx: index("api_keys_user_uuid_idx").on(table.userUuid),
    accountIdIdx: index("api_keys_account_id_idx").on(table.accountId),
    statusIdx: index("api_keys_status_idx").on(table.status),
    expiresAtIdx: index("api_keys_expires_at_idx").on(table.expiresAt),
  })
);

// Invitations table - Formal invitation system
export const invitations = pgTable(
  "invitations",
  {
    id: serial("id").primaryKey(),
    accountId: pgUuid("account_id")
      .notNull()
      .references(() => accounts.id, { onDelete: "cascade" }),
    email: varchar("email", { length: 255 }).notNull(),
    role: varchar("role", { length: 50 })
      .notNull()
      .references(() => roles.name),
    inviteToken: varchar("invite_token", { length: 255 }).notNull().unique(),
    invitedBy: pgUuid("invited_by")
      .notNull()
      .references(() => users.id),
    expiresAt: timestamp("expires_at", { withTimezone: true }).notNull(),
    acceptedAt: timestamp("accepted_at", { withTimezone: true }),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => ({
    accountIdIdx: index("invitations_account_id_idx").on(table.accountId),
    emailIdx: index("invitations_email_idx").on(table.email),
    inviteTokenIdx: index("invitations_invite_token_idx").on(table.inviteToken),
    expiresAtIdx: index("invitations_expires_at_idx").on(table.expiresAt),
    statusIdx: index("invitations_status_idx").on(table.acceptedAt), // For filtering accepted/pending
  })
);

// Notifications table - In-app notification system
export const notifications = pgTable(
  "notifications",
  {
    id: serial("id").primaryKey(),
    accountId: text("account_id")
      .notNull()
      .references(() => accounts.id, { onDelete: "cascade" }),
    userId: text("user_id").references(() => users.id, { onDelete: "cascade" }), // Optional: specific user notification
    title: text("title").notNull(),
    body: text("body").notNull(),
    type: notificationTypeEnum("type").default("info").notNull(),
    channel: notificationChannelEnum("channel").default("in_app").notNull(),
    link: text("link"), // Optional: action link
    dismissed: boolean("dismissed").default(false).notNull(),
    readAt: timestamp("read_at"),
    expiresAt: timestamp("expires_at"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
  },
  (table) => ({
    accountIdIdx: index("notifications_account_id_idx").on(table.accountId),
    userIdIdx: index("notifications_user_id_idx").on(table.userId),
    typeIdx: index("notifications_type_idx").on(table.type),
    dismissedIdx: index("notifications_dismissed_idx").on(table.dismissed),
    createdAtIdx: index("notifications_created_at_idx").on(table.createdAt),
  })
);

// Billing customers table - Multi-provider billing support
export const billingCustomers = pgTable(
  "billing_customers",
  {
    id: serial("id").primaryKey(),
    accountId: text("account_id")
      .notNull()
      .references(() => accounts.id, { onDelete: "cascade" }),
    customerId: text("customer_id").notNull(), // Provider's customer ID
    email: text("email"),
    provider: billingProviderEnum("provider").notNull(),
  },
  (table) => ({
    accountIdIdx: index("billing_customers_account_id_idx").on(table.accountId),
    customerIdProviderUnique: unique("billing_customers_customer_id_provider_unique").on(
      table.customerId,
      table.provider
    ),
  })
);

// Orders table - Enhanced for multi-provider billing
export const orders = pgTable(
  "orders",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    orderNo: varchar("order_no", { length: 100 }).notNull().unique(), // Order number
    accountId: pgUuid("account_id")
      .notNull()
      .references(() => accounts.id), // Multi-tenant support
    billingCustomerId: integer("billing_customer_id")
      .notNull()
      .references(() => billingCustomers.id),
    userUuid: pgUuid("user_uuid").notNull(), // Reference to users.uuid
    userEmail: varchar("user_email", { length: 255 }).notNull(),
    totalAmount: decimal("total_amount", { precision: 10, scale: 2 }).notNull(),
    currency: varchar("currency", { length: 3 }).notNull().default("USD"),
    status: paymentStatusEnum("status").default("pending").notNull(),
    billingProvider: billingProviderEnum("billing_provider").notNull(),
    providerOrderId: varchar("provider_order_id", { length: 255 }), // Provider's order ID
    orderDetail: jsonb("order_detail"), // Additional order details (JSON)
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => ({
    orderNoIdx: index("orders_order_no_idx").on(table.orderNo),
    accountIdIdx: index("orders_account_id_idx").on(table.accountId),
    userUuidIdx: index("orders_user_uuid_idx").on(table.userUuid),
    statusIdx: index("orders_status_idx").on(table.status),
    createdAtIdx: index("orders_created_at_idx").on(table.createdAt),
    providerIdx: index("orders_provider_idx").on(table.billingProvider),
  })
);

// Order items table - Line items for orders
export const orderItems = pgTable(
  "order_items",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    orderId: text("order_id")
      .notNull()
      .references(() => orders.id, { onDelete: "cascade" }),
    productId: text("product_id").notNull(),
    variantId: text("variant_id").notNull(),
    quantity: integer("quantity").default(1).notNull(),
    priceAmount: decimal("price_amount", { precision: 10, scale: 2 }),
    credits: integer("credits"), // Credits to be awarded
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
  },
  (table) => ({
    orderIdIdx: index("order_items_order_id_idx").on(table.orderId),
    productIdIdx: index("order_items_product_id_idx").on(table.productId),
  })
);

// Subscriptions table - Separate from one-time orders
export const subscriptions = pgTable(
  "subscriptions",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    accountId: text("account_id")
      .notNull()
      .references(() => accounts.id),
    billingCustomerId: integer("billing_customer_id")
      .notNull()
      .references(() => billingCustomers.id),
    status: subscriptionStatusEnum("status").notNull(),
    billingProvider: billingProviderEnum("billing_provider").notNull(),
    active: boolean("active").notNull(),
    cancelAtPeriodEnd: boolean("cancel_at_period_end").default(false).notNull(),
    currency: text("currency").notNull(),
    periodStartsAt: timestamp("period_starts_at").notNull(),
    periodEndsAt: timestamp("period_ends_at").notNull(),
    trialStartsAt: timestamp("trial_starts_at"),
    trialEndsAt: timestamp("trial_ends_at"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
  },
  (table) => ({
    accountIdIdx: index("subscriptions_account_id_idx").on(table.accountId),
    statusIdx: index("subscriptions_status_idx").on(table.status),
    activeIdx: index("subscriptions_active_idx").on(table.active),
    periodEndsAtIdx: index("subscriptions_period_ends_at_idx").on(table.periodEndsAt),
  })
);

// Subscription items table
export const subscriptionItems = pgTable(
  "subscription_items",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    subscriptionId: text("subscription_id")
      .notNull()
      .references(() => subscriptions.id, { onDelete: "cascade" }),
    productId: text("product_id").notNull(),
    variantId: text("variant_id").notNull(),
    quantity: integer("quantity").default(1).notNull(),
    priceAmount: decimal("price_amount", { precision: 10, scale: 2 }),
    interval: text("interval").notNull(), // month, year
    intervalCount: integer("interval_count").default(1).notNull(),
    type: text("type").notNull().default("flat"), // flat, per_seat, metered
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
  },
  (table) => ({
    subscriptionIdIdx: index("subscription_items_subscription_id_idx").on(table.subscriptionId),
    productIdIdx: index("subscription_items_product_id_idx").on(table.productId),
  })
);

// Feedback table - User feedback and support
export const feedback = pgTable("feedback", {
  id: serial("id").primaryKey(),
  userUuid: text("user_uuid"), // Optional - can be anonymous
  content: text("content").notNull(),
  rating: integer("rating"), // 1-5 rating
  status: text("status").notNull().default("created"), // "created" | "reviewed" | "resolved"
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Credit transactions table - Enhanced with expiration
export const creditTransactions = pgTable(
  "credit_transactions",
  {
    id: serial("id").primaryKey(),
    transNo: text("trans_no").notNull().unique(), // Transaction number
    userUuid: text("user_uuid").notNull(), // References users.uuid
    accountId: text("account_id").references(() => accounts.id), // Multi-tenant support
    transType: text("trans_type").notNull(), // purchase, usage, refund, bonus, affiliate_reward
    credits: integer("credits").notNull(), // Credit amount (positive or negative)
    description: text("description"), // Transaction description
    orderNo: text("order_no"), // Related order number
    expiresAt: timestamp("expires_at"), // Credit expiration date
    createdAt: timestamp("created_at").defaultNow().notNull(),
  },
  (table) => ({
    transNoIdx: index("credit_transactions_trans_no_idx").on(table.transNo),
    userUuidIdx: index("credit_transactions_user_uuid_idx").on(table.userUuid),
    accountIdIdx: index("credit_transactions_account_id_idx").on(table.accountId),
    transTypeIdx: index("credit_transactions_trans_type_idx").on(table.transType),
    createdAtIdx: index("credit_transactions_created_at_idx").on(table.createdAt),
    expiresAtIdx: index("credit_transactions_expires_at_idx").on(table.expiresAt),
  })
);

export const generatedImages = pgTable(
  "generated_images",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    r2Key: text("r2_key").notNull().unique(), // R2 object key
    userId: pgUuid("user_id").references(() => users.id, { onDelete: "set null" }), // User who generated/uploaded
    prompt: text("prompt"),
    modelUsed: varchar("model_used", { length: 100 }),
    provider: varchar("provider", { length: 50 }),
    externalSourceUrl: text("external_source_url"), // Original URL from AI provider
    imageFormat: varchar("image_format", { length: 10 }), // e.g., png, jpeg, webp
    sizeBytes: integer("size_bytes"),
    width: integer("width"),
    height: integer("height"),
    altText: text("alt_text"),
    // tags: jsonb("tags"), // For simplicity, start without tags or use text array if preferred
    customR2Metadata: jsonb("custom_r2_metadata"), // To store custom metadata from R2 object
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    deletedAt: timestamp("deleted_at", { withTimezone: true }), // For soft deletes
  },
  (table) => ({
    r2KeyIdx: index("generated_images_r2_key_idx").on(table.r2Key),
    userIdIdx: index("generated_images_user_id_idx").on(table.userId),
    createdAtIdx: index("generated_images_created_at_idx").on(table.createdAt),
  })
);

// Posts table - Keep the existing posts table for blog functionality
export const posts = pgTable(
  "posts",
  {
    id: serial("id").primaryKey(),
    title: text("title").notNull(),
    content: text("content"),
    published: boolean("published").default(false).notNull(),
    authorId: text("author_id").references(() => users.id),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
  },
  (table) => ({
    authorIdIdx: index("posts_author_id_idx").on(table.authorId),
    publishedIdx: index("posts_published_idx").on(table.published),
    createdAtIdx: index("posts_created_at_idx").on(table.createdAt),
  })
);

// Conversations table - AI chat conversations
export const conversations = pgTable(
  "conversations",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    userUuid: pgUuid("user_uuid").notNull(), // References users.uuid
    title: varchar("title", { length: 255 }).notNull(),
    model: varchar("model", { length: 100 }), // AI model used
    provider: varchar("provider", { length: 50 }), // AI provider (openai, cloudflare, etc.)
    isArchived: boolean("is_archived").default(false).notNull(),
    lastMessageAt: timestamp("last_message_at", { withTimezone: true }),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => ({
    userUuidIdx: index("conversations_user_uuid_idx").on(table.userUuid),
    lastMessageAtIdx: index("conversations_last_message_at_idx").on(table.lastMessageAt),
    createdAtIdx: index("conversations_created_at_idx").on(table.createdAt),
    isArchivedIdx: index("conversations_is_archived_idx").on(table.isArchived),
  })
);

// Messages table - Individual messages in conversations
export const messages = pgTable(
  "messages",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    conversationId: pgUuid("conversation_id")
      .notNull()
      .references(() => conversations.id, { onDelete: "cascade" }),
    role: varchar("role", { length: 20 }).notNull(), // "user" | "assistant" | "system"
    content: text("content").notNull(),
    model: varchar("model", { length: 100 }), // AI model used for this message
    provider: varchar("provider", { length: 50 }), // AI provider
    tokenCount: integer("token_count"), // Token count for this message
    metadata: jsonb("metadata"), // Additional metadata (usage, reasoning, etc.)
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => ({
    conversationIdIdx: index("messages_conversation_id_idx").on(table.conversationId),
    roleIdx: index("messages_role_idx").on(table.role),
    createdAtIdx: index("messages_created_at_idx").on(table.createdAt),
  })
);

// Relations definitions for Drizzle ORM
export const accountsRelations = relations(accounts, ({ many, one }) => ({
  memberships: many(accountsMemberships),
  invitations: many(invitations),
  notifications: many(notifications),
  billingCustomers: many(billingCustomers),
  orders: many(orders),
  subscriptions: many(subscriptions),
  creditTransactions: many(creditTransactions),
  primaryOwner: one(users, {
    fields: [accounts.primaryOwnerUserId],
    references: [users.id],
  }),
}));

export const usersRelations = relations(users, ({ many, one }) => ({
  accountMemberships: many(accountsMemberships),
  ownedAccounts: many(accounts, {
    relationName: "AccountOwner",
  }),
  affiliates: many(affiliates),
  apiKeys: many(apiKeys),
  sentInvitations: many(invitations),
  posts: many(posts),
  conversations: many(conversations),
  generatedImages: many(generatedImages),
}));

export const accountsMembershipsRelations = relations(accountsMemberships, ({ one }) => ({
  account: one(accounts, {
    fields: [accountsMemberships.accountId],
    references: [accounts.id],
  }),
  user: one(users, {
    fields: [accountsMemberships.userId],
    references: [users.id],
  }),
  role: one(roles, {
    fields: [accountsMemberships.accountRole],
    references: [roles.name],
  }),
}));

export const rolesRelations = relations(roles, ({ many }) => ({
  permissions: many(rolePermissions),
  memberships: many(accountsMemberships),
  invitations: many(invitations),
}));

export const rolePermissionsRelations = relations(rolePermissions, ({ one }) => ({
  role: one(roles, {
    fields: [rolePermissions.role],
    references: [roles.name],
  }),
}));

export const ordersRelations = relations(orders, ({ one, many }) => ({
  account: one(accounts, {
    fields: [orders.accountId],
    references: [accounts.id],
  }),
  billingCustomer: one(billingCustomers, {
    fields: [orders.billingCustomerId],
    references: [billingCustomers.id],
  }),
  items: many(orderItems),
}));

export const orderItemsRelations = relations(orderItems, ({ one }) => ({
  order: one(orders, {
    fields: [orderItems.orderId],
    references: [orders.id],
  }),
}));

export const subscriptionsRelations = relations(subscriptions, ({ one, many }) => ({
  account: one(accounts, {
    fields: [subscriptions.accountId],
    references: [accounts.id],
  }),
  billingCustomer: one(billingCustomers, {
    fields: [subscriptions.billingCustomerId],
    references: [billingCustomers.id],
  }),
  items: many(subscriptionItems),
}));

export const subscriptionItemsRelations = relations(subscriptionItems, ({ one }) => ({
  subscription: one(subscriptions, {
    fields: [subscriptionItems.subscriptionId],
    references: [subscriptions.id],
  }),
}));

export const conversationsRelations = relations(conversations, ({ one, many }) => ({
  user: one(users, {
    fields: [conversations.userUuid],
    references: [users.uuid],
  }),
  messages: many(messages),
}));

export const messagesRelations = relations(messages, ({ one }) => ({
  conversation: one(conversations, {
    fields: [messages.conversationId],
    references: [conversations.id],
  }),
}));

export const generatedImagesRelations = relations(generatedImages, ({ one }) => ({
  user: one(users, {
    fields: [generatedImages.userId],
    references: [users.id],
  }),
}));

// Type exports
export type Account = typeof accounts.$inferSelect;
export type NewAccount = typeof accounts.$inferInsert;
export type Role = typeof roles.$inferSelect;
export type NewRole = typeof roles.$inferInsert;
export type RolePermission = typeof rolePermissions.$inferSelect;
export type NewRolePermission = typeof rolePermissions.$inferInsert;
export type AccountMembership = typeof accountsMemberships.$inferSelect;
export type NewAccountMembership = typeof accountsMemberships.$inferInsert;
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Affiliate = typeof affiliates.$inferSelect;
export type NewAffiliate = typeof affiliates.$inferInsert;
export type ApiKey = typeof apiKeys.$inferSelect;
export type NewApiKey = typeof apiKeys.$inferInsert;
export type Invitation = typeof invitations.$inferSelect;
export type NewInvitation = typeof invitations.$inferInsert;
export type Notification = typeof notifications.$inferSelect;
export type NewNotification = typeof notifications.$inferInsert;
export type BillingCustomer = typeof billingCustomers.$inferSelect;
export type NewBillingCustomer = typeof billingCustomers.$inferInsert;
export type Order = typeof orders.$inferSelect;
export type NewOrder = typeof orders.$inferInsert;
export type OrderItem = typeof orderItems.$inferSelect;
export type NewOrderItem = typeof orderItems.$inferInsert;
export type Subscription = typeof subscriptions.$inferSelect;
export type NewSubscription = typeof subscriptions.$inferInsert;
export type SubscriptionItem = typeof subscriptionItems.$inferSelect;
export type NewSubscriptionItem = typeof subscriptionItems.$inferInsert;
export type Feedback = typeof feedback.$inferSelect;
export type NewFeedback = typeof feedback.$inferInsert;
export type CreditTransaction = typeof creditTransactions.$inferSelect;
export type NewCreditTransaction = typeof creditTransactions.$inferInsert;
export type Post = typeof posts.$inferSelect;
export type NewPost = typeof posts.$inferInsert;
export type Session = typeof sessions.$inferSelect;
export type NewSession = typeof sessions.$inferInsert;
export type Conversation = typeof conversations.$inferSelect;
export type NewConversation = typeof conversations.$inferInsert;
export type Message = typeof messages.$inferSelect;
export type NewMessage = typeof messages.$inferInsert;
export type GeneratedImage = typeof generatedImages.$inferSelect;
export type NewGeneratedImage = typeof generatedImages.$inferInsert;
