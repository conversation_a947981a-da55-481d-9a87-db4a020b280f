/**
 * Console Layout Component
 * Provides consistent layout for all console pages
 */

import { Link, useLocation } from "@remix-run/react";
import { cn } from "~/lib/utils";
import {
  LayoutDashboard,
  User,
  CreditCard,
  Activity,
  ShoppingCart,
  Settings,
  Menu,
} from "lucide-react";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "~/components/ui/sheet";

interface ConsoleLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
}

const navigation = [
  {
    name: "Dashboard",
    href: "/console/dashboard",
    icon: LayoutDashboard,
    description: "Overview and quick stats",
  },
  {
    name: "Profile",
    href: "/console/profile",
    icon: User,
    description: "Personal information",
  },
  {
    name: "Subscription",
    href: "/console/subscription",
    icon: CreditCard,
    description: "Manage subscription plan",
  },
  {
    name: "Credits",
    href: "/console/credits",
    icon: CreditCard,
    description: "Balance and transactions",
  },
  {
    name: "Usage Analytics",
    href: "/console/usage",
    icon: Activity,
    description: "API usage and metrics",
  },
  {
    name: "Orders",
    href: "/console/orders",
    icon: ShoppingCart,
    description: "Purchase history",
  },
  {
    name: "Settings",
    href: "/console/settings",
    icon: Settings,
    description: "Account preferences",
  },
];

function NavigationItem({
  item,
  isMobile = false,
}: { item: (typeof navigation)[0]; isMobile?: boolean }) {
  const location = useLocation();
  const isActive = location.pathname === item.href;

  return (
    <Link
      to={item.href}
      className={cn(
        "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
        isActive
          ? "bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400"
          : "text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-100 dark:hover:bg-gray-800",
        isMobile && "w-full"
      )}
    >
      <item.icon className="w-5 h-5" />
      <div className={cn("flex-1", !isMobile && "hidden lg:block")}>
        <div>{item.name}</div>
        {!isMobile && (
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">{item.description}</div>
        )}
      </div>
    </Link>
  );
}

function Sidebar() {
  return (
    <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0">
      <div className="flex flex-col flex-grow pt-5 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800">
        <div className="flex items-center flex-shrink-0 px-4">
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">AI</span>
            </div>
            <span className="text-xl font-bold text-gray-900 dark:text-white">Console</span>
          </Link>
        </div>

        <div className="mt-8 flex-grow flex flex-col">
          <nav className="flex-1 px-4 space-y-2">
            {navigation.map((item) => (
              <NavigationItem key={item.name} item={item} />
            ))}
          </nav>
        </div>

        <div className="flex-shrink-0 p-4 border-t border-gray-200 dark:border-gray-800">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Need help?{" "}
            <Link to="/docs" className="text-blue-600 hover:text-blue-500">
              View docs
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

function MobileSidebar() {
  const [open, setOpen] = useState(false);

  return (
    <div className="lg:hidden">
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="sm" className="lg:hidden">
            <Menu className="w-5 h-5" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-64 p-0">
          <div className="flex flex-col h-full">
            <div className="flex items-center justify-between p-4 border-b">
              <Link to="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">AI</span>
                </div>
                <span className="text-xl font-bold">Console</span>
              </Link>
            </div>

            <nav className="flex-1 p-4 space-y-2">
              {navigation.map((item) => (
                <NavigationItem key={item.name} item={item} isMobile />
              ))}
            </nav>

            <div className="p-4 border-t">
              <div className="text-xs text-gray-500">
                Need help?{" "}
                <Link to="/docs" className="text-blue-600">
                  View docs
                </Link>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}

export default function ConsoleLayout({ children, title, description }: ConsoleLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="lg:pl-64">
        {/* Mobile header */}
        <div className="lg:hidden flex items-center justify-between p-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
          <MobileSidebar />
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
              <span className="text-white font-bold text-xs">AI</span>
            </div>
            <span className="font-bold">Console</span>
          </Link>
          <div className="w-8" /> {/* Spacer for centering */}
        </div>

        {/* Main content */}
        <main className="flex-1">
          {(title || description) && (
            <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
              <div className="px-4 sm:px-6 lg:px-8 py-6">
                {title && (
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{title}</h1>
                )}
                {description && (
                  <p className="mt-2 text-gray-600 dark:text-gray-400">{description}</p>
                )}
              </div>
            </div>
          )}

          <div className="px-4 sm:px-6 lg:px-8 py-6">{children}</div>
        </main>
      </div>
    </div>
  );
}
