/**
 * API Route: Usage Analytics
 * Provides detailed usage analytics and statistics for users
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { requireUser } from "~/lib/auth/middleware.server";
import { createDb } from "~/lib/db/db";
import { getUserUsageAnalytics, getRecentUsage } from "~/services/usage-tracking.server";
import { respData, respErr } from "~/lib/api/resp";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // Check authentication
    const user = await requireUser(request);

    // Create database connection
    if (!context.cloudflare?.env?.DATABASE_URL) {
      return respErr("Database not available");
    }
    const db = createDb(context.cloudflare.env.DATABASE_URL);

    // Parse query parameters
    const url = new URL(request.url);
    const period = url.searchParams.get("period") || "7"; // days
    const includeRecent = url.searchParams.get("recent") === "true";

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - parseInt(period, 10));

    // Get usage analytics
    const analytics = await getUserUsageAnalytics(user.uuid, startDate, endDate, db);

    // Get recent usage if requested
    let recentUsage = [];
    if (includeRecent) {
      recentUsage = await getRecentUsage(user.uuid, 20, db);
    }

    return respData({
      analytics,
      recentUsage,
      period: {
        days: parseInt(period, 10),
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
    });
  } catch (error) {
    console.error("Error getting usage analytics:", error);
    return respErr(error instanceof Error ? error.message : "Failed to get usage analytics");
  }
}
