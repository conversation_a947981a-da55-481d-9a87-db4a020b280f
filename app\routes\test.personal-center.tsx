/**
 * Test page for personal center functionality
 * This is a development-only page to test the user personal center features
 */

import { <PERSON> } from "@remix-run/react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import {
  User,
  CreditCard,
  Activity,
  Settings,
  ShoppingCart,
  ExternalLink,
  CheckCircle,
  Clock,
  Star,
} from "lucide-react";

export default function TestPersonalCenter() {
  const testFeatures = [
    {
      name: "Profile Management",
      href: "/console/profile",
      icon: User,
      description: "Edit personal information, avatar, and bio",
      status: "completed",
      features: [
        "✅ View and edit basic profile information",
        "✅ Avatar upload via URL",
        "✅ Account information display",
        "✅ Form validation and error handling",
        "✅ Success/error message feedback",
      ],
    },
    {
      name: "Credits Management",
      href: "/console/credits",
      icon: CreditCard,
      description: "Monitor credit balance and transaction history",
      status: "completed",
      features: [
        "✅ Real-time credit balance display",
        "✅ Comprehensive transaction history",
        "✅ Transaction categorization and filtering",
        "✅ Credit statistics and analytics",
        "✅ Quick purchase options",
      ],
    },
    {
      name: "Usage Analytics",
      href: "/console/usage",
      icon: Activity,
      description: "Detailed API usage analytics and metrics",
      status: "completed",
      features: [
        "✅ Real-time usage statistics",
        "✅ Provider and model breakdowns",
        "✅ Success rate monitoring",
        "✅ Recent activity tracking",
        "✅ Interactive analytics dashboard",
      ],
    },
    {
      name: "Account Settings",
      href: "/console/settings",
      icon: Settings,
      description: "Manage account preferences and security",
      status: "completed",
      features: [
        "✅ Notification preferences",
        "✅ Invite code management",
        "✅ Security settings overview",
        "✅ Account deletion (placeholder)",
        "✅ Privacy controls",
      ],
    },
    {
      name: "Order History",
      href: "/console/orders",
      icon: ShoppingCart,
      description: "View purchase history and invoices",
      status: "existing",
      features: [
        "✅ Purchase history display",
        "✅ Order status tracking",
        "✅ Invoice downloads",
        "✅ Payment method management",
        "✅ Subscription details",
      ],
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "existing":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "in-progress":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4" />;
      case "existing":
        return <Star className="w-4 h-4" />;
      case "in-progress":
        return <Clock className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Personal Center Test Suite
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Test and explore all personal center functionality
          </p>
        </div>

        {/* Quick Navigation */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Navigation</CardTitle>
            <CardDescription>Direct links to all personal center pages</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
              {testFeatures.map((feature) => (
                <Button
                  key={feature.name}
                  asChild
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                >
                  <Link to={feature.href}>
                    <feature.icon className="w-6 h-6" />
                    <span className="text-sm font-medium text-center">{feature.name}</span>
                  </Link>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Feature Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {testFeatures.map((feature) => (
            <Card key={feature.name}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2">
                    <feature.icon className="w-5 h-5" />
                    <span>{feature.name}</span>
                  </CardTitle>
                  <Badge className={getStatusColor(feature.status)}>
                    <div className="flex items-center space-x-1">
                      {getStatusIcon(feature.status)}
                      <span className="capitalize">{feature.status}</span>
                    </div>
                  </Badge>
                </div>
                <CardDescription>{feature.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="space-y-2">
                    {feature.features.map((item, index) => (
                      <div key={index} className="text-sm text-gray-600 dark:text-gray-400">
                        {item}
                      </div>
                    ))}
                  </div>

                  <div className="pt-3 border-t">
                    <Button asChild className="w-full">
                      <Link
                        to={feature.href}
                        className="flex items-center justify-center space-x-2"
                      >
                        <span>Test {feature.name}</span>
                        <ExternalLink className="w-4 h-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Testing Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-900 dark:text-blue-400 mb-2">
                  How to Test Personal Center Features:
                </h4>
                <ol className="text-sm text-blue-800 dark:text-blue-300 space-y-1 list-decimal list-inside">
                  <li>Make sure you're logged in to test authenticated features</li>
                  <li>Click on each feature card to navigate to the respective page</li>
                  <li>Test form submissions and data updates</li>
                  <li>Verify responsive design on different screen sizes</li>
                  <li>Check error handling with invalid inputs</li>
                  <li>Test navigation between different console pages</li>
                </ol>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-green-900 dark:text-green-400 mb-2">
                  Key Features to Test:
                </h4>
                <ul className="text-sm text-green-800 dark:text-green-300 space-y-1 list-disc list-inside">
                  <li>
                    <strong>Profile Management:</strong> Update name, avatar, view account info
                  </li>
                  <li>
                    <strong>Credits:</strong> View balance, transaction history, purchase options
                  </li>
                  <li>
                    <strong>Usage Analytics:</strong> API usage stats, provider breakdowns
                  </li>
                  <li>
                    <strong>Settings:</strong> Notifications, invite codes, security settings
                  </li>
                  <li>
                    <strong>Navigation:</strong> Sidebar, mobile menu, breadcrumbs
                  </li>
                </ul>
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-yellow-900 dark:text-yellow-400 mb-2">
                  API Endpoints to Test:
                </h4>
                <ul className="text-sm text-yellow-800 dark:text-yellow-300 space-y-1 list-disc list-inside">
                  <li>
                    <code>POST /api/user/update-profile</code> - Update user information
                  </li>
                  <li>
                    <code>GET /api/usage/analytics</code> - Get usage analytics
                  </li>
                  <li>
                    <code>GET /api/user/credit-history</code> - Get credit transactions
                  </li>
                  <li>
                    <code>GET /api/get-user-info</code> - Get user profile data
                  </li>
                </ul>
              </div>

              <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-purple-900 dark:text-purple-400 mb-2">
                  Integration Points:
                </h4>
                <ul className="text-sm text-purple-800 dark:text-purple-300 space-y-1 list-disc list-inside">
                  <li>User authentication and session management</li>
                  <li>Credit system integration with usage tracking</li>
                  <li>Real-time data updates and synchronization</li>
                  <li>Form validation and error handling</li>
                  <li>Responsive design and mobile compatibility</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Console Layout Test */}
        <Card>
          <CardHeader>
            <CardTitle>Console Layout Features</CardTitle>
            <CardDescription>Test the console layout and navigation system</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">Desktop Features:</h4>
                <ul className="text-sm space-y-1 list-disc list-inside">
                  <li>Fixed sidebar with navigation</li>
                  <li>Active page highlighting</li>
                  <li>Detailed navigation descriptions</li>
                  <li>Consistent page headers</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Mobile Features:</h4>
                <ul className="text-sm space-y-1 list-disc list-inside">
                  <li>Collapsible mobile sidebar</li>
                  <li>Touch-friendly navigation</li>
                  <li>Responsive grid layouts</li>
                  <li>Mobile-optimized forms</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
