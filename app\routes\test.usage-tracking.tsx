/**
 * Test page for usage tracking functionality
 * This is a development-only page to test the usage analytics
 */

import { useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import UsageAnalytics from "~/components/dashboard/usage-analytics";
import { Activity, Zap, AlertCircle } from "lucide-react";

export default function TestUsageTracking() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const runTestRequest = async (endpoint: string, testData: any) => {
    try {
      setLoading(true);
      const startTime = Date.now();
      
      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(testData),
      });
      
      const duration = Date.now() - startTime;
      const result = await response.json();
      
      const testResult = {
        endpoint,
        status: response.ok ? "success" : "error",
        duration,
        statusCode: response.status,
        result: response.ok ? "✅ Success" : `❌ Error: ${result.error || "Unknown error"}`,
        timestamp: new Date().toLocaleTimeString(),
      };
      
      setTestResults(prev => [testResult, ...prev.slice(0, 9)]); // Keep last 10 results
      return testResult;
    } catch (error) {
      const testResult = {
        endpoint,
        status: "error",
        duration: 0,
        statusCode: 0,
        result: `❌ Network Error: ${error instanceof Error ? error.message : "Unknown error"}`,
        timestamp: new Date().toLocaleTimeString(),
      };
      
      setTestResults(prev => [testResult, ...prev.slice(0, 9)]);
      return testResult;
    } finally {
      setLoading(false);
    }
  };

  const testAITextGeneration = () => {
    return runTestRequest("/api/ai/generate-text", {
      prompt: "Write a short hello message for testing usage tracking.",
      provider: "openai",
      model: "gpt-3.5-turbo",
    });
  };

  const testAIImageGeneration = () => {
    return runTestRequest("/api/ai/generate-image", {
      prompt: "A simple test image for usage tracking",
      provider: "openai",
      model: "dall-e-2",
    });
  };

  const testCloudflareAI = () => {
    return runTestRequest("/api/ai/cloudflare", {
      prompt: "Test message for Cloudflare AI usage tracking",
      model: "llama-3.2-3b",
    });
  };

  const testInvalidRequest = () => {
    return runTestRequest("/api/ai/generate-text", {
      // Missing required fields to trigger validation error
      prompt: "",
    });
  };

  const runAllTests = async () => {
    setTestResults([]);
    await testAITextGeneration();
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    await testCloudflareAI();
    await new Promise(resolve => setTimeout(resolve, 1000));
    await testInvalidRequest();
    await new Promise(resolve => setTimeout(resolve, 1000));
    await testAIImageGeneration();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "error":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Usage Tracking Test Suite
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Test API usage tracking and analytics functionality
          </p>
        </div>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="w-5 h-5" />
              <span>API Test Controls</span>
            </CardTitle>
            <CardDescription>
              Run individual tests or all tests to generate usage data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
              <Button
                onClick={testAITextGeneration}
                disabled={loading}
                variant="outline"
                className="w-full"
              >
                <Zap className="w-4 h-4 mr-2" />
                Text Gen
              </Button>
              <Button
                onClick={testAIImageGeneration}
                disabled={loading}
                variant="outline"
                className="w-full"
              >
                <Zap className="w-4 h-4 mr-2" />
                Image Gen
              </Button>
              <Button
                onClick={testCloudflareAI}
                disabled={loading}
                variant="outline"
                className="w-full"
              >
                <Zap className="w-4 h-4 mr-2" />
                Cloudflare AI
              </Button>
              <Button
                onClick={testInvalidRequest}
                disabled={loading}
                variant="outline"
                className="w-full"
              >
                <AlertCircle className="w-4 h-4 mr-2" />
                Invalid Test
              </Button>
              <Button
                onClick={runAllTests}
                disabled={loading}
                className="w-full"
              >
                {loading ? "Running..." : "Run All Tests"}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <CardDescription>
                Recent API test results (last 10 requests)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Badge className={getStatusColor(result.status)}>
                        {result.status}
                      </Badge>
                      <div>
                        <div className="font-medium text-sm">{result.endpoint}</div>
                        <div className="text-xs text-gray-500">
                          {result.duration}ms • Status: {result.statusCode}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm">{result.result}</div>
                      <div className="text-xs text-gray-500">{result.timestamp}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Usage Analytics Dashboard */}
        <Card>
          <CardHeader>
            <CardTitle>Live Usage Analytics</CardTitle>
            <CardDescription>
              Real-time usage analytics dashboard (refresh to see new data)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <UsageAnalytics />
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-900 dark:text-blue-400 mb-2">
                  How to Test Usage Tracking:
                </h4>
                <ol className="text-sm text-blue-800 dark:text-blue-300 space-y-1 list-decimal list-inside">
                  <li>Click individual test buttons to make API calls</li>
                  <li>Watch the test results appear in real-time</li>
                  <li>Check the usage analytics dashboard below</li>
                  <li>Refresh the page to see updated analytics</li>
                  <li>Try different time periods in the analytics dropdown</li>
                </ol>
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-yellow-900 dark:text-yellow-400 mb-2">
                  What Gets Tracked:
                </h4>
                <ul className="text-sm text-yellow-800 dark:text-yellow-300 space-y-1 list-disc list-inside">
                  <li>API endpoint and HTTP method</li>
                  <li>Request/response duration and size</li>
                  <li>AI provider and model used</li>
                  <li>Tokens and credits consumed</li>
                  <li>Success/failure status and error details</li>
                  <li>User IP address and user agent</li>
                  <li>Rate limiting and quota tracking</li>
                </ul>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-green-900 dark:text-green-400 mb-2">
                  Analytics Features:
                </h4>
                <ul className="text-sm text-green-800 dark:text-green-300 space-y-1 list-disc list-inside">
                  <li>Real-time usage statistics and metrics</li>
                  <li>Success rate and error tracking</li>
                  <li>Provider and model usage breakdown</li>
                  <li>Daily usage trends and patterns</li>
                  <li>Recent API call history</li>
                  <li>Cost estimation and credit tracking</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
