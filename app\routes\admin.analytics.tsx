/**
 * Admin Analytics Dashboard
 * Comprehensive system-wide analytics and insights for administrators
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { useLoaderData, useSearchParams } from "@remix-run/react";
import { useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { createDbFromEnv } from "~/lib/db";
import {
  getAdminAnalytics,
  type AdminAnalytics,
  type DateRange,
} from "~/services/analytics.server";
import {
  Users,
  DollarSign,
  TrendingUp,
  Activity,
  BarChart3,
  Star,
  MessageSquare,
  ShoppingCart,
  CreditCard,
  Zap,
  Clock,
  RefreshCw,
  Download,
  Filter,
  ArrowUp,
  ArrowDown,
} from "lucide-react";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    // TODO: Add admin authentication check

    const url = new URL(request.url);
    const period = url.searchParams.get("period") || "30";
    const days = parseInt(period, 10);

    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const dateRange: DateRange = { startDate, endDate };
    const analytics = await getAdminAnalytics(dateRange, db);

    return json({
      success: true,
      data: {
        analytics,
        period: {
          days,
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        },
      },
    });
  } catch (error) {
    console.error("Error loading admin analytics:", error);
    return json({ success: false, error: "Failed to load analytics" }, { status: 500 });
  }
}

export default function AdminAnalyticsPage() {
  const { data } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedTab, setSelectedTab] = useState("overview");

  const analytics: AdminAnalytics = data.analytics;
  const period = data.period;

  const handlePeriodChange = (newPeriod: string) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("period", newPeriod);
    setSearchParams(newParams);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M";
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K";
    }
    return num.toString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const calculateGrowthRate = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Admin Analytics</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                System-wide analytics and business insights
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Select value={period.days.toString()} onValueChange={handlePeriodChange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 90 days</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="w-8 h-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Users
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatNumber(analytics.overview.totalUsers)}
                  </p>
                  <p className="text-xs text-green-600 flex items-center">
                    <ArrowUp className="w-3 h-3 mr-1" />
                    {analytics.overview.activeUsers} active
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <DollarSign className="w-8 h-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Revenue
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatCurrency(analytics.overview.totalRevenue)}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatCurrency(analytics.overview.avgRevenuePerUser)} per user
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Activity className="w-8 h-8 text-purple-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">API Calls</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatNumber(analytics.overview.totalApiCalls)}
                  </p>
                  <p className="text-xs text-gray-500">
                    {analytics.overview.totalApiCalls > 0
                      ? Math.round(
                          analytics.overview.totalApiCalls / analytics.overview.activeUsers
                        )
                      : 0}{" "}
                    per active user
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <ShoppingCart className="w-8 h-8 text-orange-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Orders</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatNumber(analytics.overview.totalOrders)}
                  </p>
                  <p className="text-xs text-gray-500">
                    {analytics.overview.totalSubscriptions} subscriptions
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Analytics */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="revenue">Revenue</TabsTrigger>
            <TabsTrigger value="usage">Usage</TabsTrigger>
            <TabsTrigger value="feedback">Feedback</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Monthly Revenue */}
              <Card>
                <CardHeader>
                  <CardTitle>Monthly Revenue</CardTitle>
                  <CardDescription>Revenue for the last 30 days</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {formatCurrency(analytics.overview.monthlyRevenue)}
                  </div>
                  <p className="text-sm text-gray-500">
                    {analytics.revenue.dailyRevenue.length > 0 && (
                      <>Avg daily: {formatCurrency(analytics.overview.monthlyRevenue / 30)}</>
                    )}
                  </p>
                </CardContent>
              </Card>

              {/* User Growth */}
              <Card>
                <CardHeader>
                  <CardTitle>User Growth</CardTitle>
                  <CardDescription>New user registrations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {analytics.users.userGrowth.growth}
                  </div>
                  <p className="text-sm text-gray-500">New users in selected period</p>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity Summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <MessageSquare className="w-5 h-5" />
                    <span>Feedback</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Total</span>
                      <span className="font-medium">{analytics.feedback.totalFeedback}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg Rating</span>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="font-medium">{analytics.feedback.averageRating}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CreditCard className="w-5 h-5" />
                    <span>Subscriptions</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Active</span>
                      <span className="font-medium">
                        {analytics.revenue.subscriptionMetrics.active}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Churn Rate</span>
                      <span className="font-medium">
                        {analytics.revenue.subscriptionMetrics.churnRate.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Zap className="w-5 h-5" />
                    <span>API Usage</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Total Calls</span>
                      <span className="font-medium">
                        {formatNumber(analytics.usage.totalApiCalls)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Top Provider</span>
                      <span className="font-medium">
                        {Object.entries(analytics.usage.byProvider).sort(
                          ([, a], [, b]) => b - a
                        )[0]?.[0] || "N/A"}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* New Users Trend */}
              <Card>
                <CardHeader>
                  <CardTitle>New Users Trend</CardTitle>
                  <CardDescription>Daily new user registrations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analytics.users.newUsers.slice(-7).map((day) => (
                      <div key={day.date} className="flex items-center justify-between">
                        <span className="text-sm">{new Date(day.date).toLocaleDateString()}</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full"
                              style={{
                                width: `${(day.count / Math.max(...analytics.users.newUsers.map((d) => d.count), 1)) * 100}%`,
                              }}
                            />
                          </div>
                          <span className="font-medium w-8">{day.count}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Top Users */}
              <Card>
                <CardHeader>
                  <CardTitle>Top Users by Usage</CardTitle>
                  <CardDescription>Most active users in the selected period</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analytics.users.topUsers.slice(0, 10).map((user, index) => (
                      <div key={user.userUuid} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className="text-sm font-medium w-4">#{index + 1}</span>
                          <div>
                            <p className="font-medium">{user.userName}</p>
                            <p className="text-xs text-gray-500">{user.userEmail}</p>
                          </div>
                        </div>
                        <div className="text-right text-sm">
                          <p className="font-medium">{user.totalRequests} requests</p>
                          <p className="text-gray-500">{formatCurrency(user.totalSpent)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="revenue" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Daily Revenue */}
              <Card>
                <CardHeader>
                  <CardTitle>Daily Revenue</CardTitle>
                  <CardDescription>Revenue breakdown by day</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analytics.revenue.dailyRevenue.slice(-7).map((day) => (
                      <div key={day.date} className="flex items-center justify-between">
                        <span className="text-sm">{new Date(day.date).toLocaleDateString()}</span>
                        <div className="text-right">
                          <p className="font-medium">{formatCurrency(day.revenue)}</p>
                          <p className="text-xs text-gray-500">{day.orders} orders</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Revenue Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle>Revenue Metrics</CardTitle>
                  <CardDescription>Key revenue indicators</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>Total Revenue</span>
                      <span className="font-bold">
                        {formatCurrency(analytics.overview.totalRevenue)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Monthly Revenue</span>
                      <span className="font-medium">
                        {formatCurrency(analytics.overview.monthlyRevenue)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg Revenue per User</span>
                      <span className="font-medium">
                        {formatCurrency(analytics.overview.avgRevenuePerUser)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Active Subscriptions</span>
                      <span className="font-medium">
                        {analytics.revenue.subscriptionMetrics.active}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="usage" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Provider Usage */}
              <Card>
                <CardHeader>
                  <CardTitle>Usage by Provider</CardTitle>
                  <CardDescription>API calls by AI provider</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analytics.usage.byProvider)
                      .sort(([, a], [, b]) => b - a)
                      .map(([provider, count]) => (
                        <div key={provider} className="flex items-center justify-between">
                          <span className="capitalize">{provider}</span>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{formatNumber(count)}</span>
                            <Badge variant="outline">
                              {((count / analytics.usage.totalApiCalls) * 100).toFixed(1)}%
                            </Badge>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>

              {/* Top Endpoints */}
              <Card>
                <CardHeader>
                  <CardTitle>Top Endpoints</CardTitle>
                  <CardDescription>Most used API endpoints</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analytics.usage.topEndpoints.map((endpoint) => (
                      <div key={endpoint.endpoint} className="flex items-center justify-between">
                        <span className="text-sm font-mono">{endpoint.endpoint}</span>
                        <div className="text-right text-sm">
                          <p className="font-medium">{formatNumber(endpoint.requests)}</p>
                          <p className="text-gray-500">{endpoint.avgDuration}ms avg</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="feedback" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Feedback by Type */}
              <Card>
                <CardHeader>
                  <CardTitle>Feedback by Type</CardTitle>
                  <CardDescription>Distribution of feedback types</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analytics.feedback.byType)
                      .sort(([, a], [, b]) => b - a)
                      .map(([type, count]) => (
                        <div key={type} className="flex items-center justify-between">
                          <span className="capitalize">{type.replace("_", " ")}</span>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{count}</span>
                            <Badge variant="outline">
                              {((count / analytics.feedback.totalFeedback) * 100).toFixed(1)}%
                            </Badge>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Feedback */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Feedback</CardTitle>
                  <CardDescription>Latest user feedback</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analytics.feedback.recentFeedback.map((feedback) => (
                      <div key={feedback.id} className="border-b pb-3 last:border-b-0">
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium text-sm">{feedback.title}</span>
                          <div className="flex items-center space-x-1">
                            {feedback.rating && (
                              <>
                                <Star className="w-3 h-3 text-yellow-400 fill-current" />
                                <span className="text-xs">{feedback.rating}</span>
                              </>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <Badge variant="outline" className="text-xs">
                            {feedback.type.replace("_", " ")}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {feedback.status}
                          </Badge>
                          <span>{new Date(feedback.createdAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
