/**
 * Credits Management Page
 * Shows credit balance, transaction history, and purchase options
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { Link, useLoaderData } from "@remix-run/react";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { createDbFromEnv } from "~/lib/db";
import { findUserByUuid, getUserCreditHistory } from "~/models/user";
import { getUserUuid } from "~/services/user";
import {
  CreditCard,
  TrendingUp,
  TrendingDown,
  Plus,
  Minus,
  Calendar,
  DollarSign,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCw,
  Gift,
  ShoppingCart,
} from "lucide-react";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const user = await findUserByUuid(userUuid, db);
    if (!user) {
      return json({ success: false, error: "User not found" }, { status: 404 });
    }

    // Get credit transaction history
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1", 10);
    const limit = parseInt(url.searchParams.get("limit") || "20", 10);

    const creditHistory = await getUserCreditHistory(userUuid, db, {
      page,
      limit,
    });

    // Calculate statistics
    const totalEarned = creditHistory.data
      .filter((tx) => tx.credits > 0)
      .reduce((sum, tx) => sum + tx.credits, 0);

    const totalSpent = creditHistory.data
      .filter((tx) => tx.credits < 0)
      .reduce((sum, tx) => sum + Math.abs(tx.credits), 0);

    return json({
      success: true,
      data: {
        user: {
          uuid: user.uuid,
          name: user.name,
          email: user.email,
          credits: user.credits,
        },
        creditHistory: creditHistory.data,
        pagination: creditHistory.pagination,
        stats: {
          currentBalance: user.credits,
          totalEarned,
          totalSpent,
          netChange: totalEarned - totalSpent,
        },
      },
    });
  } catch (error) {
    console.error("Error loading credits page:", error);
    return json({ success: false, error: "Failed to load credits data" }, { status: 500 });
  }
}

export default function CreditsPage() {
  const { data } = useLoaderData<typeof loader>();
  const [selectedPeriod, setSelectedPeriod] = useState("all");

  const getTransactionIcon = (type: string, credits: number) => {
    if (credits > 0) {
      switch (type) {
        case "purchase":
          return <ShoppingCart className="w-4 h-4 text-green-500" />;
        case "reward":
        case "invite_bonus":
        case "referral":
          return <Gift className="w-4 h-4 text-blue-500" />;
        default:
          return <ArrowUpRight className="w-4 h-4 text-green-500" />;
      }
    } else {
      return <ArrowDownRight className="w-4 h-4 text-red-500" />;
    }
  };

  const getTransactionColor = (credits: number) => {
    return credits > 0 ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400";
  };

  const getTransactionBadgeColor = (type: string) => {
    switch (type) {
      case "purchase":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "reward":
      case "invite_bonus":
      case "referral":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "ai_text_generation":
      case "ai_image_generation":
      case "ai_stream_text":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const formatTransactionType = (type: string) => {
    return type
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Credits Management</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Monitor your credit balance and transaction history
          </p>
        </div>

        {/* Credit Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Balance</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {data.stats.currentBalance.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">Available credits</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Earned</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {data.stats.totalEarned.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">Credits received</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
              <TrendingDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                {data.stats.totalSpent.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">Credits used</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Net Change</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div
                className={`text-2xl font-bold ${
                  data.stats.netChange >= 0
                    ? "text-green-600 dark:text-green-400"
                    : "text-red-600 dark:text-red-400"
                }`}
              >
                {data.stats.netChange >= 0 ? "+" : ""}
                {data.stats.netChange.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">Overall balance</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Manage your credits and account</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button asChild className="w-full">
                  <Link to="/pricing" className="flex items-center justify-center space-x-2">
                    <Plus className="w-4 h-4" />
                    <span>Buy More Credits</span>
                  </Link>
                </Button>

                <Button variant="outline" asChild className="w-full">
                  <Link to="/console/usage" className="flex items-center justify-center space-x-2">
                    <RefreshCw className="w-4 h-4" />
                    <span>View Usage Analytics</span>
                  </Link>
                </Button>

                <Button variant="outline" asChild className="w-full">
                  <Link to="/console/orders" className="flex items-center justify-center space-x-2">
                    <ShoppingCart className="w-4 h-4" />
                    <span>Purchase History</span>
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Credit Packages */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Popular Packages</CardTitle>
                <CardDescription>Quick purchase options</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="p-3 border rounded-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="font-semibold">Starter Pack</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">1,000 credits</div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">$10</div>
                      <div className="text-xs text-gray-500">$0.01/credit</div>
                    </div>
                  </div>
                </div>

                <div className="p-3 border rounded-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="font-semibold">Pro Pack</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">5,000 credits</div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">$40</div>
                      <div className="text-xs text-gray-500">$0.008/credit</div>
                    </div>
                  </div>
                </div>

                <div className="p-3 border rounded-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="font-semibold">Enterprise Pack</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">20,000 credits</div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">$120</div>
                      <div className="text-xs text-gray-500">$0.006/credit</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Transaction History */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="w-5 h-5" />
                  <span>Transaction History</span>
                </CardTitle>
                <CardDescription>Complete history of your credit transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="all">All Time</TabsTrigger>
                    <TabsTrigger value="month">This Month</TabsTrigger>
                    <TabsTrigger value="week">This Week</TabsTrigger>
                    <TabsTrigger value="today">Today</TabsTrigger>
                  </TabsList>

                  <TabsContent value={selectedPeriod} className="mt-6">
                    <div className="space-y-4">
                      {data.creditHistory.length === 0 ? (
                        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                          <CreditCard className="w-12 h-12 mx-auto mb-4 opacity-50" />
                          <p>No transactions found</p>
                          <p className="text-sm">Your credit transactions will appear here</p>
                        </div>
                      ) : (
                        data.creditHistory.map((transaction) => (
                          <div
                            key={transaction.id}
                            className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                          >
                            <div className="flex items-center space-x-3">
                              {getTransactionIcon(transaction.trans_type, transaction.credits)}
                              <div>
                                <div className="font-medium">
                                  {formatTransactionType(transaction.trans_type)}
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-400">
                                  {new Date(transaction.created_at).toLocaleString()}
                                </div>
                                {transaction.description && (
                                  <div className="text-xs text-gray-500 mt-1">
                                    {transaction.description}
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="text-right">
                              <div
                                className={`font-bold ${getTransactionColor(transaction.credits)}`}
                              >
                                {transaction.credits > 0 ? "+" : ""}
                                {transaction.credits.toLocaleString()}
                              </div>
                              <Badge className={getTransactionBadgeColor(transaction.trans_type)}>
                                {formatTransactionType(transaction.trans_type)}
                              </Badge>
                            </div>
                          </div>
                        ))
                      )}
                    </div>

                    {/* Pagination */}
                    {data.pagination.totalPages > 1 && (
                      <div className="flex justify-center mt-6">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={data.pagination.currentPage === 1}
                          >
                            Previous
                          </Button>
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            Page {data.pagination.currentPage} of {data.pagination.totalPages}
                          </span>
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={data.pagination.currentPage === data.pagination.totalPages}
                          >
                            Next
                          </Button>
                        </div>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
