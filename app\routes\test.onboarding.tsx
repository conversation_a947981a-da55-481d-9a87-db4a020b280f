/**
 * Test page for onboarding flow
 * This is a development-only page to test the welcome flow
 */

import { useState } from "react";
import { Button } from "~/components/ui/button";
import WelcomeFlow from "~/components/onboarding/welcome-flow";

export default function TestOnboarding() {
  const [showWelcomeFlow, setShowWelcomeFlow] = useState(false);

  const mockUser = {
    name: "<PERSON>",
    email: "<EMAIL>",
    credits: 150,
    inviteCode: "ABC12345",
  };

  const handleOnboardingComplete = () => {
    console.log("Onboarding completed!");
    setShowWelcomeFlow(false);
  };

  const handleOnboardingSkip = () => {
    console.log("Onboarding skipped!");
    setShowWelcomeFlow(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Onboarding Flow Test</h1>
          <p className="text-gray-600 mb-6">Test the user onboarding welcome flow</p>
        </div>

        <div className="space-y-4">
          <Button onClick={() => setShowWelcomeFlow(true)} className="w-full" size="lg">
            🎉 Show Welcome Flow
          </Button>

          <div className="bg-white p-4 rounded-lg border">
            <h3 className="font-semibold mb-2">Mock User Data:</h3>
            <div className="text-sm text-gray-600 space-y-1">
              <div>Name: {mockUser.name}</div>
              <div>Email: {mockUser.email}</div>
              <div>Credits: {mockUser.credits}</div>
              <div>Invite Code: {mockUser.inviteCode}</div>
            </div>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h3 className="font-semibold text-blue-900 mb-2">Test Instructions:</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Click "Show Welcome Flow" to test the onboarding</li>
              <li>• Navigate through the steps using Next/Previous</li>
              <li>• Test the "Skip Tour" functionality</li>
              <li>• Check console for completion/skip events</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Welcome Flow Component */}
      {showWelcomeFlow && (
        <WelcomeFlow
          user={mockUser}
          isNewUser={true}
          onComplete={handleOnboardingComplete}
          onSkip={handleOnboardingSkip}
        />
      )}
    </div>
  );
}
