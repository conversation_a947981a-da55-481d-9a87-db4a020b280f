/**
 * API Route: Update User Profile
 * Handles user profile updates including name, avatar, and preferences
 */

import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { createDbFromEnv } from "~/lib/db";
import { findUserByUuid, updateUser } from "~/models/user";
import { getUserUuid } from "~/services/user";
import { respData, respErr } from "~/lib/api/resp";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("Authentication required");
    }

    const user = await findUserByUuid(userUuid, db);
    if (!user) {
      return respErr("User not found");
    }

    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case "update-basic-info": {
        const { name, bio } = data;

        // Validate input
        if (!name || typeof name !== "string" || name.trim().length < 2) {
          return respErr("Name must be at least 2 characters long");
        }

        if (bio && typeof bio !== "string") {
          return respErr("Bio must be a string");
        }

        // Update user
        await updateUser(
          userUuid,
          {
            name: name.trim(),
            // Note: bio field would need to be added to schema
          },
          db
        );

        return respData({
          message: "Profile updated successfully",
          user: {
            name: name.trim(),
            bio: bio || null,
          },
        });
      }

      case "update-avatar": {
        const { avatarUrl } = data;

        if (avatarUrl && !isValidUrl(avatarUrl)) {
          return respErr("Please provide a valid image URL");
        }

        await updateUser(
          userUuid,
          {
            avatar: avatarUrl || null,
          },
          db
        );

        return respData({
          message: "Avatar updated successfully",
          user: {
            avatar: avatarUrl || null,
          },
        });
      }

      case "update-preferences": {
        const { preferences } = data;

        // TODO: Implement user preferences update
        // This would require adding a preferences field to the user schema

        return respData({
          message: "Preferences updated successfully",
          preferences,
        });
      }

      case "regenerate-invite-code": {
        const newInviteCode = generateInviteCode();

        await updateUser(
          userUuid,
          {
            inviteCode: newInviteCode,
          },
          db
        );

        return respData({
          message: "Invite code regenerated successfully",
          inviteCode: newInviteCode,
        });
      }

      default:
        return respErr("Invalid action");
    }
  } catch (error) {
    console.error("Error updating user profile:", error);
    return respErr(error instanceof Error ? error.message : "Failed to update profile");
  }
}

function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

function generateInviteCode(): string {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = "";
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
