/**
 * Error Tracking and Logging System
 * Comprehensive error tracking, logging, and analysis
 */

export interface ErrorEvent {
  id: string;
  timestamp: Date;
  level: "error" | "warning" | "info" | "debug";
  message: string;
  stack?: string;
  context: ErrorContext;
  metadata?: Record<string, any>;
  fingerprint: string; // For grouping similar errors
  resolved: boolean;
  resolvedAt?: Date;
  occurrenceCount: number;
  firstOccurrence: Date;
  lastOccurrence: Date;
}

export interface ErrorContext {
  userUuid?: string;
  sessionId?: string;
  requestId?: string;
  endpoint?: string;
  method?: string;
  userAgent?: string;
  ipAddress?: string;
  environment: string;
  version?: string;
  component?: string;
  operation?: string;
}

export interface ErrorSummary {
  totalErrors: number;
  errorsByLevel: Record<string, number>;
  errorsByComponent: Record<string, number>;
  errorsByEndpoint: Record<string, number>;
  topErrors: Array<{
    fingerprint: string;
    message: string;
    count: number;
    lastOccurrence: Date;
  }>;
  errorRate: number;
  recentErrors: ErrorEvent[];
}

export interface LogEntry {
  id: string;
  timestamp: Date;
  level: "error" | "warning" | "info" | "debug";
  message: string;
  context: LogContext;
  metadata?: Record<string, any>;
}

export interface LogContext {
  component: string;
  operation?: string;
  userUuid?: string;
  requestId?: string;
  endpoint?: string;
  duration?: number;
}

export interface LoggerConfig {
  enabled: boolean;
  level: "error" | "warning" | "info" | "debug";
  maxEntries: number;
  enableConsole: boolean;
  enableFile: boolean;
  enableRemote: boolean;
  retentionDays: number;
}

// Default logger configuration
export const DEFAULT_LOGGER_CONFIG: LoggerConfig = {
  enabled: true,
  level: "info",
  maxEntries: 10000,
  enableConsole: true,
  enableFile: false, // Not available in Cloudflare Workers
  enableRemote: false,
  retentionDays: 30,
};

// In-memory storage for errors and logs
const errorEvents: ErrorEvent[] = [];
const logEntries: LogEntry[] = [];
const errorFingerprints: Map<string, ErrorEvent> = new Map();

let loggerConfig = DEFAULT_LOGGER_CONFIG;

/**
 * Initialize error tracker
 */
export function initErrorTracker(config: Partial<LoggerConfig> = {}): void {
  loggerConfig = { ...DEFAULT_LOGGER_CONFIG, ...config };

  if (!loggerConfig.enabled) {
    console.log("[ERROR_TRACKER] Error tracking disabled");
    return;
  }

  // Set up global error handlers
  setupGlobalErrorHandlers();

  console.log("[ERROR_TRACKER] Error tracking initialized");
}

/**
 * Set up global error handlers
 */
function setupGlobalErrorHandlers(): void {
  // Handle unhandled promise rejections
  if (typeof process !== "undefined") {
    process.on("unhandledRejection", (reason, promise) => {
      trackError(new Error(`Unhandled Promise Rejection: ${reason}`), {
        component: "system",
        operation: "unhandledRejection",
        environment: process.env.NODE_ENV || "unknown",
      });
    });

    process.on("uncaughtException", (error) => {
      trackError(error, {
        component: "system",
        operation: "uncaughtException",
        environment: process.env.NODE_ENV || "unknown",
      });
    });
  }

  // Handle window errors (if in browser context)
  if (typeof window !== "undefined") {
    window.addEventListener("error", (event) => {
      trackError(event.error || new Error(event.message), {
        component: "client",
        operation: "windowError",
        environment: "browser",
        endpoint: window.location.pathname,
      });
    });

    window.addEventListener("unhandledrejection", (event) => {
      trackError(new Error(`Unhandled Promise Rejection: ${event.reason}`), {
        component: "client",
        operation: "unhandledRejection",
        environment: "browser",
        endpoint: window.location.pathname,
      });
    });
  }
}

/**
 * Track an error event
 */
export function trackError(
  error: Error | string,
  context: Partial<ErrorContext> = {},
  metadata?: Record<string, any>
): string {
  const errorObj = typeof error === "string" ? new Error(error) : error;
  const fingerprint = generateErrorFingerprint(errorObj, context);

  // Check if this error already exists
  const existingError = errorFingerprints.get(fingerprint);

  if (existingError) {
    // Update existing error
    existingError.occurrenceCount++;
    existingError.lastOccurrence = new Date();
    existingError.metadata = { ...existingError.metadata, ...metadata };

    log(
      "debug",
      `Error occurred again: ${errorObj.message}`,
      {
        component: context.component || "unknown",
        operation: context.operation,
        userUuid: context.userUuid,
        requestId: context.requestId,
      },
      { fingerprint, occurrenceCount: existingError.occurrenceCount }
    );

    return existingError.id;
  }

  // Create new error event
  const errorEvent: ErrorEvent = {
    id: generateErrorId(),
    timestamp: new Date(),
    level: "error",
    message: errorObj.message,
    stack: errorObj.stack,
    context: {
      environment: process.env.NODE_ENV || "unknown",
      ...context,
    },
    metadata,
    fingerprint,
    resolved: false,
    occurrenceCount: 1,
    firstOccurrence: new Date(),
    lastOccurrence: new Date(),
  };

  // Store error
  errorEvents.push(errorEvent);
  errorFingerprints.set(fingerprint, errorEvent);

  // Keep only recent errors
  if (errorEvents.length > loggerConfig.maxEntries) {
    const removed = errorEvents.splice(0, errorEvents.length - loggerConfig.maxEntries);
    removed.forEach((err) => errorFingerprints.delete(err.fingerprint));
  }

  // Log error
  log(
    "error",
    `Error tracked: ${errorObj.message}`,
    {
      component: context.component || "unknown",
      operation: context.operation,
      userUuid: context.userUuid,
      requestId: context.requestId,
    },
    { fingerprint, stack: errorObj.stack }
  );

  // Console output if enabled
  if (loggerConfig.enableConsole) {
    console.error(`[ERROR_TRACKER] ${errorObj.message}`, {
      id: errorEvent.id,
      fingerprint,
      context: errorEvent.context,
      stack: errorObj.stack,
    });
  }

  return errorEvent.id;
}

/**
 * Log a message
 */
export function log(
  level: "error" | "warning" | "info" | "debug",
  message: string,
  context: Partial<LogContext> = {},
  metadata?: Record<string, any>
): string {
  if (!loggerConfig.enabled) return "";

  // Check log level
  const levels = ["error", "warning", "info", "debug"];
  const currentLevelIndex = levels.indexOf(loggerConfig.level);
  const messageLevelIndex = levels.indexOf(level);

  if (messageLevelIndex > currentLevelIndex) {
    return ""; // Skip this log level
  }

  const logEntry: LogEntry = {
    id: generateLogId(),
    timestamp: new Date(),
    level,
    message,
    context: {
      component: "unknown",
      ...context,
    },
    metadata,
  };

  // Store log entry
  logEntries.push(logEntry);

  // Keep only recent logs
  if (logEntries.length > loggerConfig.maxEntries) {
    logEntries.splice(0, logEntries.length - loggerConfig.maxEntries);
  }

  // Console output if enabled
  if (loggerConfig.enableConsole) {
    const consoleMethod =
      level === "error"
        ? "error"
        : level === "warning"
          ? "warn"
          : level === "debug"
            ? "debug"
            : "log";

    console[consoleMethod](`[${level.toUpperCase()}] [${logEntry.context.component}] ${message}`, {
      id: logEntry.id,
      context: logEntry.context,
      metadata: logEntry.metadata,
    });
  }

  return logEntry.id;
}

/**
 * Generate error fingerprint for grouping
 */
function generateErrorFingerprint(error: Error, context: Partial<ErrorContext>): string {
  const message = error.message.replace(/\d+/g, "N"); // Replace numbers with N
  const component = context.component || "unknown";
  const operation = context.operation || "unknown";

  // Simple hash function
  const content = `${component}:${operation}:${message}`;
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  return Math.abs(hash).toString(36);
}

/**
 * Generate unique error ID
 */
function generateErrorId(): string {
  return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Generate unique log ID
 */
function generateLogId(): string {
  return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Get error summary
 */
export function getErrorSummary(): ErrorSummary {
  const now = Date.now();
  const oneHourAgo = now - 60 * 60 * 1000;
  const recentErrors = errorEvents.filter((err) => err.lastOccurrence.getTime() > oneHourAgo);

  // Count errors by level
  const errorsByLevel: Record<string, number> = {};
  errorEvents.forEach((err) => {
    errorsByLevel[err.level] = (errorsByLevel[err.level] || 0) + err.occurrenceCount;
  });

  // Count errors by component
  const errorsByComponent: Record<string, number> = {};
  errorEvents.forEach((err) => {
    const component = err.context.component || "unknown";
    errorsByComponent[component] = (errorsByComponent[component] || 0) + err.occurrenceCount;
  });

  // Count errors by endpoint
  const errorsByEndpoint: Record<string, number> = {};
  errorEvents.forEach((err) => {
    if (err.context.endpoint) {
      errorsByEndpoint[err.context.endpoint] =
        (errorsByEndpoint[err.context.endpoint] || 0) + err.occurrenceCount;
    }
  });

  // Get top errors
  const topErrors = [...errorEvents]
    .sort((a, b) => b.occurrenceCount - a.occurrenceCount)
    .slice(0, 10)
    .map((err) => ({
      fingerprint: err.fingerprint,
      message: err.message,
      count: err.occurrenceCount,
      lastOccurrence: err.lastOccurrence,
    }));

  // Calculate error rate (errors per hour)
  const totalOccurrences = errorEvents.reduce((sum, err) => sum + err.occurrenceCount, 0);
  const errorRate = recentErrors.reduce((sum, err) => sum + err.occurrenceCount, 0);

  return {
    totalErrors: errorEvents.length,
    errorsByLevel,
    errorsByComponent,
    errorsByEndpoint,
    topErrors,
    errorRate,
    recentErrors: recentErrors.slice(-20),
  };
}

/**
 * Get error events with filtering
 */
export function getErrorEvents(
  options: {
    limit?: number;
    offset?: number;
    level?: string;
    component?: string;
    resolved?: boolean;
    startDate?: Date;
    endDate?: Date;
  } = {}
): ErrorEvent[] {
  let filtered = [...errorEvents];

  if (options.level) {
    filtered = filtered.filter((err) => err.level === options.level);
  }

  if (options.component) {
    filtered = filtered.filter((err) => err.context.component === options.component);
  }

  if (options.resolved !== undefined) {
    filtered = filtered.filter((err) => err.resolved === options.resolved);
  }

  if (options.startDate) {
    filtered = filtered.filter((err) => err.timestamp >= options.startDate!);
  }

  if (options.endDate) {
    filtered = filtered.filter((err) => err.timestamp <= options.endDate!);
  }

  // Sort by last occurrence (newest first)
  filtered.sort((a, b) => b.lastOccurrence.getTime() - a.lastOccurrence.getTime());

  // Apply pagination
  const offset = options.offset || 0;
  const limit = options.limit || 100;

  return filtered.slice(offset, offset + limit);
}

/**
 * Get log entries with filtering
 */
export function getLogEntries(
  options: {
    limit?: number;
    offset?: number;
    level?: string;
    component?: string;
    startDate?: Date;
    endDate?: Date;
  } = {}
): LogEntry[] {
  let filtered = [...logEntries];

  if (options.level) {
    filtered = filtered.filter((log) => log.level === options.level);
  }

  if (options.component) {
    filtered = filtered.filter((log) => log.context.component === options.component);
  }

  if (options.startDate) {
    filtered = filtered.filter((log) => log.timestamp >= options.startDate!);
  }

  if (options.endDate) {
    filtered = filtered.filter((log) => log.timestamp <= options.endDate!);
  }

  // Sort by timestamp (newest first)
  filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

  // Apply pagination
  const offset = options.offset || 0;
  const limit = options.limit || 100;

  return filtered.slice(offset, offset + limit);
}

/**
 * Resolve an error
 */
export function resolveError(errorId: string): boolean {
  const error = errorEvents.find((err) => err.id === errorId);
  if (error) {
    error.resolved = true;
    error.resolvedAt = new Date();

    log(
      "info",
      `Error resolved: ${error.message}`,
      {
        component: "error-tracker",
        operation: "resolveError",
      },
      { errorId, fingerprint: error.fingerprint }
    );

    return true;
  }
  return false;
}

/**
 * Clear old logs and errors
 */
export function clearOldEntries(olderThanDays: number = 30): { errors: number; logs: number } {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

  const initialErrorCount = errorEvents.length;
  const initialLogCount = logEntries.length;

  // Filter errors
  const filteredErrors = errorEvents.filter((err) => err.timestamp >= cutoffDate);
  errorEvents.length = 0;
  errorEvents.push(...filteredErrors);

  // Update fingerprint map
  errorFingerprints.clear();
  filteredErrors.forEach((err) => errorFingerprints.set(err.fingerprint, err));

  // Filter logs
  const filteredLogs = logEntries.filter((log) => log.timestamp >= cutoffDate);
  logEntries.length = 0;
  logEntries.push(...filteredLogs);

  const removedErrors = initialErrorCount - errorEvents.length;
  const removedLogs = initialLogCount - logEntries.length;

  if (removedErrors > 0 || removedLogs > 0) {
    log("info", `Cleared old entries: ${removedErrors} errors, ${removedLogs} logs`, {
      component: "error-tracker",
      operation: "clearOldEntries",
    });
  }

  return { errors: removedErrors, logs: removedLogs };
}

/**
 * Create logger for specific component
 */
export function createLogger(component: string) {
  return {
    error: (message: string, context?: Partial<LogContext>, metadata?: Record<string, any>) =>
      log("error", message, { ...context, component }, metadata),

    warning: (message: string, context?: Partial<LogContext>, metadata?: Record<string, any>) =>
      log("warning", message, { ...context, component }, metadata),

    info: (message: string, context?: Partial<LogContext>, metadata?: Record<string, any>) =>
      log("info", message, { ...context, component }, metadata),

    debug: (message: string, context?: Partial<LogContext>, metadata?: Record<string, any>) =>
      log("debug", message, { ...context, component }, metadata),

    trackError: (
      error: Error | string,
      context?: Partial<ErrorContext>,
      metadata?: Record<string, any>
    ) => trackError(error, { ...context, component }, metadata),
  };
}

// Initialize error tracker on module load
initErrorTracker();
