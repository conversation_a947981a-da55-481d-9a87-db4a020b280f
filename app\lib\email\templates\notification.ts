/**
 * General Notification Email Template
 * Used for various types of notifications
 */

import type { EmailTemplate } from "../templating.server";

export interface NotificationTemplateVariables {
  name: string;
  title: string;
  body: string;
  type: string;
  actionUrl?: string;
  actionText?: string;
  unsubscribeUrl?: string;
}

export const notificationTemplate: EmailTemplate<NotificationTemplateVariables> = {
  subject: (variables) => `${variables.title} - Your App`,
  
  html: (variables) => `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${variables.title}</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #f8fafc;
        }
        .container {
          background-color: white;
          border-radius: 8px;
          padding: 32px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .header {
          text-align: center;
          margin-bottom: 32px;
        }
        .logo {
          font-size: 24px;
          font-weight: bold;
          color: #2563eb;
          margin-bottom: 8px;
        }
        .notification-type {
          display: inline-block;
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
          margin-bottom: 16px;
        }
        .type-info { background-color: #dbeafe; color: #1e40af; }
        .type-success { background-color: #dcfce7; color: #166534; }
        .type-warning { background-color: #fef3c7; color: #92400e; }
        .type-error { background-color: #fee2e2; color: #dc2626; }
        .type-payment { background-color: #dbeafe; color: #1e40af; }
        .type-credit { background-color: #fef3c7; color: #92400e; }
        .type-security { background-color: #fee2e2; color: #dc2626; }
        .type-system { background-color: #f3f4f6; color: #374151; }
        .title {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 16px;
          color: #111827;
        }
        .body {
          font-size: 16px;
          margin-bottom: 24px;
          color: #4b5563;
        }
        .action-button {
          display: inline-block;
          background-color: #2563eb;
          color: white;
          padding: 12px 24px;
          text-decoration: none;
          border-radius: 6px;
          font-weight: 600;
          margin: 16px 0;
        }
        .action-button:hover {
          background-color: #1d4ed8;
        }
        .footer {
          margin-top: 32px;
          padding-top: 24px;
          border-top: 1px solid #e5e7eb;
          text-align: center;
          font-size: 14px;
          color: #6b7280;
        }
        .unsubscribe {
          color: #9ca3af;
          text-decoration: none;
          font-size: 12px;
        }
        .unsubscribe:hover {
          color: #6b7280;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">Your App</div>
          <div class="notification-type type-${variables.type}">
            ${variables.type}
          </div>
        </div>
        
        <div class="title">${variables.title}</div>
        <div class="body">${variables.body}</div>
        
        ${variables.actionUrl && variables.actionText ? `
          <div style="text-align: center;">
            <a href="${variables.actionUrl}" class="action-button">
              ${variables.actionText}
            </a>
          </div>
        ` : ''}
        
        <div class="footer">
          <p>Hi ${variables.name},</p>
          <p>This notification was sent to keep you updated about your account activity.</p>
          <p>If you have any questions, please contact our support team.</p>
          
          ${variables.unsubscribeUrl ? `
            <p>
              <a href="${variables.unsubscribeUrl}" class="unsubscribe">
                Unsubscribe from these notifications
              </a>
            </p>
          ` : ''}
        </div>
      </div>
    </body>
    </html>
  `,
  
  text: (variables) => `
${variables.title}

Hi ${variables.name},

${variables.body}

${variables.actionUrl && variables.actionText ? `
${variables.actionText}: ${variables.actionUrl}
` : ''}

This notification was sent to keep you updated about your account activity.
If you have any questions, please contact our support team.

${variables.unsubscribeUrl ? `
Unsubscribe: ${variables.unsubscribeUrl}
` : ''}

---
Your App Team
  `,
};
