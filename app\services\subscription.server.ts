/**
 * Subscription Management Service
 * Handles subscription operations, billing cycles, and Stripe integration
 */

import { eq, and, desc } from "drizzle-orm";
import type { Database } from "~/lib/db/db";
import { subscriptions, subscriptionItems, users, orders } from "~/lib/db/schema";
import { getStripeClient } from "~/lib/payment/stripe.server";
import type Stripe from "stripe";

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: "month" | "year";
  credits: number;
  features: string[];
  stripePriceId?: string;
  stripeProductId?: string;
}

export interface UserSubscription {
  id: string;
  status: string;
  active: boolean;
  cancelAtPeriodEnd: boolean;
  currency: string;
  periodStartsAt: Date;
  periodEndsAt: Date;
  plan: SubscriptionPlan;
  stripeSubscriptionId?: string;
  stripeCustomerId?: string;
}

export interface SubscriptionOperation {
  type: "upgrade" | "downgrade" | "cancel" | "reactivate";
  fromPlan?: string;
  toPlan?: string;
  effectiveDate?: Date;
  prorationAmount?: number;
}

/**
 * Get available subscription plans
 */
export function getSubscriptionPlans(): SubscriptionPlan[] {
  return [
    {
      id: "starter",
      name: "Starter",
      description: "Perfect for individuals and small projects",
      price: 0,
      currency: "usd",
      interval: "month",
      credits: 100,
      features: [
        "100 AI credits per month",
        "Basic text generation",
        "5 image generations",
        "Community support",
        "Standard response time",
      ],
    },
    {
      id: "pro_monthly",
      name: "Pro",
      description: "For professionals and growing teams",
      price: 1999, // $19.99
      currency: "usd",
      interval: "month",
      credits: 2000,
      features: [
        "2,000 AI credits per month",
        "Advanced text generation",
        "50 image generations",
        "Priority support",
        "Fast response time",
        "Usage analytics",
      ],
      stripePriceId: "price_pro_monthly",
      stripeProductId: "prod_pro",
    },
    {
      id: "pro_yearly",
      name: "Pro (Annual)",
      description: "For professionals and growing teams - Save 20%",
      price: 19190, // $191.90 (20% discount)
      currency: "usd",
      interval: "year",
      credits: 24000, // 2000 * 12
      features: [
        "24,000 AI credits per year",
        "Advanced text generation",
        "600 image generations",
        "Priority support",
        "Fast response time",
        "Usage analytics",
        "20% annual discount",
      ],
      stripePriceId: "price_pro_yearly",
      stripeProductId: "prod_pro",
    },
    {
      id: "enterprise_monthly",
      name: "Enterprise",
      description: "For large teams and organizations",
      price: 9999, // $99.99
      currency: "usd",
      interval: "month",
      credits: 10000,
      features: [
        "10,000 AI credits per month",
        "All AI models and features",
        "Unlimited image generations",
        "24/7 dedicated support",
        "Custom integrations",
        "Advanced analytics",
        "Team management",
        "SLA guarantee",
      ],
      stripePriceId: "price_enterprise_monthly",
      stripeProductId: "prod_enterprise",
    },
    {
      id: "enterprise_yearly",
      name: "Enterprise (Annual)",
      description: "For large teams and organizations - Save 25%",
      price: 89991, // $899.91 (25% discount)
      currency: "usd",
      interval: "year",
      credits: 120000, // 10000 * 12
      features: [
        "120,000 AI credits per year",
        "All AI models and features",
        "Unlimited image generations",
        "24/7 dedicated support",
        "Custom integrations",
        "Advanced analytics",
        "Team management",
        "SLA guarantee",
        "25% annual discount",
      ],
      stripePriceId: "price_enterprise_yearly",
      stripeProductId: "prod_enterprise",
    },
  ];
}

/**
 * Get user's current subscription
 */
export async function getUserSubscription(
  userUuid: string,
  db: Database
): Promise<UserSubscription | null> {
  try {
    const result = await db
      .select({
        subscription: subscriptions,
        subscriptionItem: subscriptionItems,
      })
      .from(subscriptions)
      .leftJoin(subscriptionItems, eq(subscriptions.id, subscriptionItems.subscriptionId))
      .where(
        and(
          eq(subscriptions.accountId, userUuid), // Assuming accountId maps to userUuid
          eq(subscriptions.active, true)
        )
      )
      .orderBy(desc(subscriptions.createdAt))
      .limit(1);

    if (!result || result.length === 0) {
      return null;
    }

    const { subscription, subscriptionItem } = result[0];
    const plans = getSubscriptionPlans();
    const plan = plans.find((p) => p.id === subscriptionItem?.productId) || plans[0];

    return {
      id: subscription.id,
      status: subscription.status,
      active: subscription.active,
      cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
      currency: subscription.currency,
      periodStartsAt: subscription.periodStartsAt,
      periodEndsAt: subscription.periodEndsAt,
      plan,
      stripeSubscriptionId: subscription.stripeSubscriptionId,
      stripeCustomerId: subscription.stripeCustomerId,
    };
  } catch (error) {
    console.error("Error getting user subscription:", error);
    return null;
  }
}

/**
 * Create subscription checkout session
 */
export async function createSubscriptionCheckout(
  userUuid: string,
  planId: string,
  db: Database,
  stripeSecretKey: string,
  webUrl: string
): Promise<{ success: boolean; sessionId?: string; error?: string }> {
  try {
    const plans = getSubscriptionPlans();
    const plan = plans.find((p) => p.id === planId);

    if (!plan || !plan.stripePriceId) {
      return { success: false, error: "Invalid subscription plan" };
    }

    const user = await db.select().from(users).where(eq(users.uuid, userUuid)).limit(1);

    if (!user || user.length === 0) {
      return { success: false, error: "User not found" };
    }

    const stripe = getStripeClient(stripeSecretKey);

    const session = await stripe.checkout.sessions.create({
      mode: "subscription",
      payment_method_types: ["card"],
      line_items: [
        {
          price: plan.stripePriceId,
          quantity: 1,
        },
      ],
      customer_email: user[0].email,
      metadata: {
        user_uuid: userUuid,
        plan_id: planId,
      },
      subscription_data: {
        metadata: {
          user_uuid: userUuid,
          plan_id: planId,
        },
      },
      success_url: `${webUrl}/console/subscription?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${webUrl}/pricing?canceled=true`,
      allow_promotion_codes: true,
    });

    return {
      success: true,
      sessionId: session.id,
    };
  } catch (error) {
    console.error("Error creating subscription checkout:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create checkout session",
    };
  }
}

/**
 * Cancel subscription
 */
export async function cancelSubscription(
  userUuid: string,
  cancelAtPeriodEnd: boolean,
  db: Database,
  stripeSecretKey: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const subscription = await getUserSubscription(userUuid, db);
    if (!subscription || !subscription.stripeSubscriptionId) {
      return { success: false, error: "No active subscription found" };
    }

    const stripe = getStripeClient(stripeSecretKey);

    if (cancelAtPeriodEnd) {
      // Cancel at period end
      await stripe.subscriptions.update(subscription.stripeSubscriptionId, {
        cancel_at_period_end: true,
      });

      // Update local database
      await db
        .update(subscriptions)
        .set({
          cancelAtPeriodEnd: true,
          updatedAt: new Date(),
        })
        .where(eq(subscriptions.id, subscription.id));
    } else {
      // Cancel immediately
      await stripe.subscriptions.cancel(subscription.stripeSubscriptionId);

      // Update local database
      await db
        .update(subscriptions)
        .set({
          status: "canceled",
          active: false,
          updatedAt: new Date(),
        })
        .where(eq(subscriptions.id, subscription.id));
    }

    return { success: true };
  } catch (error) {
    console.error("Error canceling subscription:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to cancel subscription",
    };
  }
}

/**
 * Reactivate canceled subscription
 */
export async function reactivateSubscription(
  userUuid: string,
  db: Database,
  stripeSecretKey: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const subscription = await getUserSubscription(userUuid, db);
    if (!subscription || !subscription.stripeSubscriptionId) {
      return { success: false, error: "No subscription found" };
    }

    if (!subscription.cancelAtPeriodEnd) {
      return { success: false, error: "Subscription is not scheduled for cancellation" };
    }

    const stripe = getStripeClient(stripeSecretKey);

    // Reactivate subscription
    await stripe.subscriptions.update(subscription.stripeSubscriptionId, {
      cancel_at_period_end: false,
    });

    // Update local database
    await db
      .update(subscriptions)
      .set({
        cancelAtPeriodEnd: false,
        updatedAt: new Date(),
      })
      .where(eq(subscriptions.id, subscription.id));

    return { success: true };
  } catch (error) {
    console.error("Error reactivating subscription:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to reactivate subscription",
    };
  }
}

/**
 * Calculate proration amount for plan change
 */
export async function calculateProration(
  userUuid: string,
  newPlanId: string,
  db: Database,
  stripeSecretKey: string
): Promise<{ success: boolean; amount?: number; error?: string }> {
  try {
    const subscription = await getUserSubscription(userUuid, db);
    if (!subscription || !subscription.stripeSubscriptionId) {
      return { success: false, error: "No active subscription found" };
    }

    const plans = getSubscriptionPlans();
    const newPlan = plans.find((p) => p.id === newPlanId);
    if (!newPlan || !newPlan.stripePriceId) {
      return { success: false, error: "Invalid new plan" };
    }

    const stripe = getStripeClient(stripeSecretKey);

    // Get upcoming invoice to calculate proration
    const upcomingInvoice = await stripe.invoices.retrieveUpcoming({
      customer: subscription.stripeCustomerId!,
      subscription: subscription.stripeSubscriptionId,
      subscription_items: [
        {
          id: subscription.id, // This should be the subscription item ID
          price: newPlan.stripePriceId,
        },
      ],
    });

    return {
      success: true,
      amount: upcomingInvoice.amount_due,
    };
  } catch (error) {
    console.error("Error calculating proration:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to calculate proration",
    };
  }
}
