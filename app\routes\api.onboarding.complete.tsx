/**
 * API Route: Complete User Onboarding
 * Marks user as having completed the onboarding flow
 */

import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { requireUser } from "~/lib/auth/middleware.server";
import { markOnboardingComplete } from "~/services/onboarding.server";
import { respData, respErr } from "~/lib/api/resp";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  try {
    const db = context.db;
    if (!db) {
      return respErr("Database not available");
    }

    // Get authenticated user
    const user = await requireUser(request);

    // Mark onboarding as complete
    const result = await markOnboardingComplete(user.uuid, db);

    if (!result.success) {
      return respErr(result.error || "Failed to complete onboarding");
    }

    return respData({
      success: true,
      message: "Onboarding completed successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error completing onboarding:", error);
    return respErr(error instanceof Error ? error.message : "Failed to complete onboarding");
  }
}

// GET method for checking onboarding status
export async function loader({ request, context }: ActionFunctionArgs) {
  try {
    const db = context.db;
    if (!db) {
      return json({ error: "Database not available" }, { status: 500 });
    }

    // Get authenticated user
    const user = await requireUser(request);

    // Check if user should see onboarding
    const { shouldShowOnboarding } = await import("~/services/onboarding.server");
    const showOnboarding = shouldShowOnboarding(user, request);

    return json({
      success: true,
      data: {
        userUuid: user.uuid,
        showOnboarding,
        isNewUser: new URL(request.url).searchParams.get("new_user") === "true",
      },
    });
  } catch (error) {
    console.error("Error checking onboarding status:", error);
    return json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Failed to check onboarding status",
      },
      { status: 500 }
    );
  }
}
