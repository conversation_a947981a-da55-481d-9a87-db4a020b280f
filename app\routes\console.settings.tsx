/**
 * Account Settings Page
 * Comprehensive account management and preferences
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { Form, Link, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Switch } from "~/components/ui/switch";
import { Separator } from "~/components/ui/separator";
import { Badge } from "~/components/ui/badge";
import { createDbFromEnv } from "~/lib/db";
import { findUserByUuid, updateUser } from "~/models/user";
import { getUserUuid } from "~/services/user";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Key,
  Trash2,
  AlertT<PERSON>gle,
  Save,
  CheckCircle,
  AlertCircle,
  Copy,
  RefreshCw,
  ExternalLink,
} from "lucide-react";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const user = await findUserByUuid(userUuid, db);
    if (!user) {
      return json({ success: false, error: "User not found" }, { status: 404 });
    }

    return json({
      success: true,
      data: {
        user: {
          uuid: user.uuid,
          name: user.name,
          email: user.email,
          avatar: user.avatar,
          credits: user.credits,
          inviteCode: user.invite_code,
          createdAt: user.created_at,
        },
        settings: {
          emailNotifications: true, // TODO: Get from user preferences
          pushNotifications: false,
          marketingEmails: true,
          usageAlerts: true,
          securityAlerts: true,
        },
      },
    });
  } catch (error) {
    console.error("Error loading settings:", error);
    return json({ success: false, error: "Failed to load settings" }, { status: 500 });
  }
}

export async function action({ request, context }: ActionFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const formData = await request.formData();
    const action = formData.get("action") as string;

    if (action === "update-notifications") {
      // TODO: Implement notification preferences update
      return json({
        success: true,
        message: "Notification preferences updated successfully",
      });
    }

    if (action === "regenerate-invite-code") {
      // Generate new invite code
      const newInviteCode = generateInviteCode();
      await updateUser(userUuid, { inviteCode: newInviteCode }, db);

      return json({
        success: true,
        message: "Invite code regenerated successfully",
        data: { newInviteCode },
      });
    }

    if (action === "delete-account") {
      // TODO: Implement account deletion (soft delete)
      return json({
        success: false,
        error: "Account deletion is not yet implemented. Please contact support.",
      });
    }

    return json({ success: false, error: "Invalid action" });
  } catch (error) {
    console.error("Error updating settings:", error);
    return json({ success: false, error: "Failed to update settings" }, { status: 500 });
  }
}

function generateInviteCode(): string {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = "";
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export default function SettingsPage() {
  const { data } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [copiedInviteCode, setCopiedInviteCode] = useState(false);

  const isSubmitting = navigation.state === "submitting";

  const copyInviteCode = async () => {
    if (data.user.inviteCode) {
      await navigator.clipboard.writeText(data.user.inviteCode);
      setCopiedInviteCode(true);
      setTimeout(() => setCopiedInviteCode(false), 2000);
    }
  };

  const inviteUrl = `${typeof window !== "undefined" ? window.location.origin : ""}/auth/register?invite=${data.user.inviteCode}`;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Account Settings</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Manage your account preferences and security settings
          </p>
        </div>

        {/* Success/Error Messages */}
        {actionData && (
          <div
            className={`mb-6 p-4 rounded-lg flex items-center space-x-2 ${
              actionData.success
                ? "bg-green-50 text-green-800 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
                : "bg-red-50 text-red-800 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"
            }`}
          >
            {actionData.success ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <AlertCircle className="w-5 h-5" />
            )}
            <span>{actionData.message || actionData.error}</span>
          </div>
        )}

        <div className="space-y-8">
          {/* Notification Preferences */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bell className="w-5 h-5" />
                <span>Notification Preferences</span>
              </CardTitle>
              <CardDescription>
                Choose how you want to receive notifications and updates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form method="post" className="space-y-6">
                <input type="hidden" name="action" value="update-notifications" />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Email Notifications</Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Receive important updates via email
                      </p>
                    </div>
                    <Switch defaultChecked={data.settings.emailNotifications} />
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Usage Alerts</Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Get notified when you're running low on credits
                      </p>
                    </div>
                    <Switch defaultChecked={data.settings.usageAlerts} />
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Security Alerts</Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Important security and account notifications
                      </p>
                    </div>
                    <Switch defaultChecked={data.settings.securityAlerts} />
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Marketing Emails</Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Product updates and promotional content
                      </p>
                    </div>
                    <Switch defaultChecked={data.settings.marketingEmails} />
                  </div>
                </div>

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex items-center space-x-2"
                >
                  <Save className="w-4 h-4" />
                  <span>{isSubmitting ? "Saving..." : "Save Preferences"}</span>
                </Button>
              </Form>
            </CardContent>
          </Card>

          {/* Invite Code Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Key className="w-5 h-5" />
                <span>Invite Code</span>
              </CardTitle>
              <CardDescription>
                Share your invite code with friends to earn bonus credits
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {data.user.inviteCode ? (
                <>
                  <div>
                    <Label>Your Invite Code</Label>
                    <div className="flex items-center space-x-2 mt-1">
                      <Input value={data.user.inviteCode} readOnly className="font-mono" />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={copyInviteCode}
                        className="flex items-center space-x-1"
                      >
                        <Copy className="w-4 h-4" />
                        <span>{copiedInviteCode ? "Copied!" : "Copy"}</span>
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label>Invite URL</Label>
                    <div className="flex items-center space-x-2 mt-1">
                      <Input value={inviteUrl} readOnly className="text-sm" />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => navigator.clipboard.writeText(inviteUrl)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Form method="post" className="inline">
                      <input type="hidden" name="action" value="regenerate-invite-code" />
                      <Button type="submit" variant="outline" size="sm">
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Regenerate Code
                      </Button>
                    </Form>
                    <Badge variant="secondary">Earn 25 credits per successful referral</Badge>
                  </div>
                </>
              ) : (
                <div className="text-center py-4">
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    No invite code generated yet
                  </p>
                  <Form method="post" className="inline">
                    <input type="hidden" name="action" value="regenerate-invite-code" />
                    <Button type="submit">Generate Invite Code</Button>
                  </Form>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Security Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="w-5 h-5" />
                <span>Security & Privacy</span>
              </CardTitle>
              <CardDescription>Manage your account security and privacy settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Account Type</Label>
                  <div className="mt-1">
                    <Badge variant="secondary">Standard User</Badge>
                  </div>
                </div>
                <div>
                  <Label>Two-Factor Authentication</Label>
                  <div className="mt-1">
                    <Badge variant="outline">Not Enabled</Badge>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <Button variant="outline" asChild className="w-full justify-start">
                  <Link to="/console/api-keys" className="flex items-center space-x-2">
                    <Key className="w-4 h-4" />
                    <span>Manage API Keys</span>
                    <ExternalLink className="w-4 h-4 ml-auto" />
                  </Link>
                </Button>

                <Button variant="outline" disabled className="w-full justify-start">
                  <Shield className="w-4 h-4 mr-2" />
                  <span>Enable Two-Factor Authentication</span>
                  <Badge variant="secondary" className="ml-auto">
                    Coming Soon
                  </Badge>
                </Button>

                <Button variant="outline" disabled className="w-full justify-start">
                  <Key className="w-4 h-4 mr-2" />
                  <span>Change Password</span>
                  <Badge variant="secondary" className="ml-auto">
                    OAuth Only
                  </Badge>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Danger Zone */}
          <Card className="border-red-200 dark:border-red-800">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-red-600 dark:text-red-400">
                <AlertTriangle className="w-5 h-5" />
                <span>Danger Zone</span>
              </CardTitle>
              <CardDescription>Irreversible and destructive actions</CardDescription>
            </CardHeader>
            <CardContent>
              {!showDeleteConfirm ? (
                <Button
                  variant="destructive"
                  onClick={() => setShowDeleteConfirm(true)}
                  className="flex items-center space-x-2"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>Delete Account</span>
                </Button>
              ) : (
                <div className="space-y-4">
                  <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                    <h4 className="font-semibold text-red-800 dark:text-red-400 mb-2">
                      Are you absolutely sure?
                    </h4>
                    <p className="text-sm text-red-700 dark:text-red-300 mb-4">
                      This action cannot be undone. This will permanently delete your account,
                      remove all your data, and cancel any active subscriptions.
                    </p>
                    <div className="flex space-x-2">
                      <Form method="post" className="inline">
                        <input type="hidden" name="action" value="delete-account" />
                        <Button type="submit" variant="destructive" size="sm">
                          Yes, delete my account
                        </Button>
                      </Form>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowDeleteConfirm(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
