/**
 * Notification Helper Functions
 * Provides convenient functions for creating common notification types
 */

import type { Database } from "~/lib/db/db";
import { createNotification, type NotificationType } from "./notification.server";

/**
 * Send payment success notification
 */
export async function notifyPaymentSuccess(
  userUuid: string,
  params: {
    amount: string;
    currency: string;
    planName: string;
    creditsAdded: number;
    nextBillingDate?: string;
    subscriptionUrl?: string;
    invoiceUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification({
      accountId: userUuid,
      title: "Payment Successful",
      body: `Your payment of ${params.amount} ${params.currency.toUpperCase()} for ${params.planName} has been processed. ${params.creditsAdded} credits have been added to your account.`,
      type: "payment",
      channel: "both",
      link: params.subscriptionUrl || "/console/subscription",
      emailTemplate: "payment-success",
      emailVariables: params,
    }, db);
  } catch (error) {
    console.error("Error sending payment success notification:", error);
  }
}

/**
 * Send payment failed notification
 */
export async function notifyPaymentFailed(
  userUuid: string,
  params: {
    amount: string;
    currency: string;
    planName: string;
    retryUrl?: string;
    supportUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification({
      accountId: userUuid,
      title: "Payment Failed",
      body: `We couldn't process your payment of ${params.amount} ${params.currency.toUpperCase()} for ${params.planName}. Please update your payment method to continue your subscription.`,
      type: "error",
      channel: "both",
      link: params.retryUrl || "/console/subscription",
      emailTemplate: "notification",
      emailVariables: {
        actionUrl: params.retryUrl || "/console/subscription",
        actionText: "Update Payment Method",
        ...params,
      },
    }, db);
  } catch (error) {
    console.error("Error sending payment failed notification:", error);
  }
}

/**
 * Send credits added notification
 */
export async function notifyCreditsAdded(
  userUuid: string,
  params: {
    creditsAdded: number;
    reason: string;
    newBalance: number;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification({
      accountId: userUuid,
      title: "Credits Added",
      body: `${params.creditsAdded} credits have been added to your account. ${params.reason}. Your new balance is ${params.newBalance} credits.`,
      type: "credit",
      channel: "in_app",
      link: "/console/credits",
    }, db);
  } catch (error) {
    console.error("Error sending credits added notification:", error);
  }
}

/**
 * Send low credits warning
 */
export async function notifyLowCredits(
  userUuid: string,
  params: {
    currentBalance: number;
    threshold: number;
    upgradeUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification({
      accountId: userUuid,
      title: "Low Credits Warning",
      body: `Your credit balance is running low (${params.currentBalance} credits remaining). Consider upgrading your plan or purchasing more credits to continue using our services.`,
      type: "warning",
      channel: "both",
      link: params.upgradeUrl || "/pricing",
      emailTemplate: "notification",
      emailVariables: {
        actionUrl: params.upgradeUrl || "/pricing",
        actionText: "Upgrade Plan",
        ...params,
      },
    }, db);
  } catch (error) {
    console.error("Error sending low credits notification:", error);
  }
}

/**
 * Send subscription cancelled notification
 */
export async function notifySubscriptionCancelled(
  userUuid: string,
  params: {
    planName: string;
    endDate: string;
    reactivateUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification({
      accountId: userUuid,
      title: "Subscription Cancelled",
      body: `Your ${params.planName} subscription has been cancelled and will end on ${params.endDate}. You can reactivate it anytime before then.`,
      type: "warning",
      channel: "both",
      link: params.reactivateUrl || "/console/subscription",
      emailTemplate: "notification",
      emailVariables: {
        actionUrl: params.reactivateUrl || "/console/subscription",
        actionText: "Reactivate Subscription",
        ...params,
      },
    }, db);
  } catch (error) {
    console.error("Error sending subscription cancelled notification:", error);
  }
}

/**
 * Send subscription reactivated notification
 */
export async function notifySubscriptionReactivated(
  userUuid: string,
  params: {
    planName: string;
    nextBillingDate: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification({
      accountId: userUuid,
      title: "Subscription Reactivated",
      body: `Your ${params.planName} subscription has been reactivated. Your next billing date is ${params.nextBillingDate}.`,
      type: "success",
      channel: "both",
      link: "/console/subscription",
      emailTemplate: "notification",
      emailVariables: params,
    }, db);
  } catch (error) {
    console.error("Error sending subscription reactivated notification:", error);
  }
}

/**
 * Send plan upgrade notification
 */
export async function notifyPlanUpgrade(
  userUuid: string,
  params: {
    fromPlan: string;
    toPlan: string;
    creditsAdded: number;
    effectiveDate: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification({
      accountId: userUuid,
      title: "Plan Upgraded",
      body: `Your subscription has been upgraded from ${params.fromPlan} to ${params.toPlan}. ${params.creditsAdded} additional credits have been added to your account.`,
      type: "success",
      channel: "both",
      link: "/console/subscription",
      emailTemplate: "notification",
      emailVariables: params,
    }, db);
  } catch (error) {
    console.error("Error sending plan upgrade notification:", error);
  }
}

/**
 * Send security alert notification
 */
export async function notifySecurityAlert(
  userUuid: string,
  params: {
    alertType: string;
    description: string;
    actionRequired?: boolean;
    actionUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification({
      accountId: userUuid,
      title: `Security Alert: ${params.alertType}`,
      body: `${params.description}${params.actionRequired ? " Please review and take action if necessary." : ""}`,
      type: "security",
      channel: "both",
      link: params.actionUrl || "/console/settings",
      emailTemplate: "notification",
      emailVariables: {
        actionUrl: params.actionUrl || "/console/settings",
        actionText: params.actionRequired ? "Review Security" : "View Details",
        ...params,
      },
    }, db);
  } catch (error) {
    console.error("Error sending security alert notification:", error);
  }
}

/**
 * Send system maintenance notification
 */
export async function notifySystemMaintenance(
  userUuid: string,
  params: {
    maintenanceType: string;
    startTime: string;
    endTime: string;
    affectedServices: string[];
    statusUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification({
      accountId: userUuid,
      title: "Scheduled Maintenance",
      body: `${params.maintenanceType} is scheduled from ${params.startTime} to ${params.endTime}. Affected services: ${params.affectedServices.join(", ")}.`,
      type: "system",
      channel: "both",
      link: params.statusUrl || "/status",
      emailTemplate: "notification",
      emailVariables: {
        actionUrl: params.statusUrl || "/status",
        actionText: "View Status Page",
        ...params,
      },
      expiresAt: new Date(params.endTime),
    }, db);
  } catch (error) {
    console.error("Error sending system maintenance notification:", error);
  }
}

/**
 * Send welcome notification for new users
 */
export async function notifyWelcome(
  userUuid: string,
  params: {
    name: string;
    startingCredits: number;
    gettingStartedUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    await createNotification({
      accountId: userUuid,
      title: "Welcome to Your App!",
      body: `Welcome ${params.name}! Your account has been created with ${params.startingCredits} free credits. Start exploring our AI-powered features today.`,
      type: "success",
      channel: "both",
      link: params.gettingStartedUrl || "/console/dashboard",
      emailTemplate: "welcome",
      emailVariables: params,
    }, db);
  } catch (error) {
    console.error("Error sending welcome notification:", error);
  }
}

/**
 * Send API usage limit notification
 */
export async function notifyUsageLimit(
  userUuid: string,
  params: {
    limitType: "rate" | "quota";
    currentUsage: number;
    limit: number;
    resetTime?: string;
    upgradeUrl?: string;
  },
  db: Database
): Promise<void> {
  try {
    const title = params.limitType === "rate" ? "Rate Limit Reached" : "Usage Quota Exceeded";
    const body = params.limitType === "rate" 
      ? `You've reached your API rate limit (${params.currentUsage}/${params.limit} requests). ${params.resetTime ? `Limit resets at ${params.resetTime}.` : ""}`
      : `You've exceeded your usage quota (${params.currentUsage}/${params.limit}). Consider upgrading your plan for higher limits.`;

    await createNotification({
      accountId: userUuid,
      title,
      body,
      type: "warning",
      channel: "in_app",
      link: params.upgradeUrl || "/console/usage",
      emailTemplate: "notification",
      emailVariables: {
        actionUrl: params.upgradeUrl || "/console/usage",
        actionText: "View Usage",
        ...params,
      },
    }, db);
  } catch (error) {
    console.error("Error sending usage limit notification:", error);
  }
}
